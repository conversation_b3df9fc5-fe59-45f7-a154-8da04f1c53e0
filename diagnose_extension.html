<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari扩展诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Safari扩展诊断工具</h1>
    
    <div class="section">
        <h3>1. 扩展状态检查</h3>
        <div id="extension-status" class="status info">检查中...</div>
        <button class="btn-primary" onclick="checkExtension()">重新检查扩展</button>
    </div>
    
    <div class="section">
        <h3>2. 页面环境检查</h3>
        <div id="page-status" class="status info">检查中...</div>
        <button class="btn-primary" onclick="checkPage()">检查页面环境</button>
    </div>
    
    <div class="section">
        <h3>3. 视频元素检查</h3>
        <div id="video-status" class="status info">检查中...</div>
        <button class="btn-primary" onclick="checkVideo()">检查视频元素</button>
    </div>
    
    <div class="section">
        <h3>4. 功能测试</h3>
        <button class="btn-success" onclick="testPopupMessage()">测试Popup消息</button>
        <button class="btn-success" onclick="testDirectFunction()">直接调用函数</button>
        <button class="btn-warning" onclick="injectTestCode()">注入测试代码</button>
    </div>
    
    <div class="section">
        <h3>5. 实时日志</h3>
        <button class="btn-primary" onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        const logDiv = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${getLogColor(type)};">${message}</span>`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function getLogColor(type) {
            switch(type) {
                case 'error': return '#dc3545';
                case 'warning': return '#ffc107';
                case 'success': return '#28a745';
                default: return '#007bff';
            }
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function checkExtension() {
            log('开始检查Safari扩展状态...');
            
            // 检查Chrome扩展API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('✓ Chrome扩展API可用', 'success');
                updateStatus('extension-status', '扩展API可用', 'success');
                
                // 检查扩展ID
                if (chrome.runtime.id) {
                    log(`✓ 扩展ID: ${chrome.runtime.id}`, 'success');
                } else {
                    log('✗ 无法获取扩展ID', 'error');
                }
                
                // 检查消息发送功能
                try {
                    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                        if (chrome.runtime.lastError) {
                            log(`✗ 扩展通信失败: ${chrome.runtime.lastError.message}`, 'error');
                        } else {
                            log('✓ 扩展通信正常', 'success');
                        }
                    });
                } catch (error) {
                    log(`✗ 扩展通信异常: ${error}`, 'error');
                }
                
            } else {
                log('✗ Chrome扩展API不可用', 'error');
                updateStatus('extension-status', '扩展API不可用', 'error');
            }
        }
        
        function checkPage() {
            log('开始检查页面环境...');
            
            // 检查URL
            log(`当前URL: ${window.location.href}`);
            
            // 检查是否在bilibili
            if (window.location.hostname.includes('bilibili.com')) {
                log('✓ 在bilibili网站', 'success');
                updateStatus('page-status', '在bilibili网站', 'success');
            } else {
                log('✗ 不在bilibili网站', 'warning');
                updateStatus('page-status', '不在bilibili网站', 'warning');
            }
            
            // 检查页面加载状态
            log(`页面加载状态: ${document.readyState}`);
            
            // 检查是否有content script注入的函数
            if (typeof window.testVideo === 'function') {
                log('✓ 检测到content script函数', 'success');
            } else {
                log('✗ 未检测到content script函数', 'error');
            }
            
            // 检查是否有WebSocket相关变量
            if (typeof window.testWebSocket === 'function') {
                log('✓ 检测到WebSocket测试函数', 'success');
            } else {
                log('✗ 未检测到WebSocket测试函数', 'error');
            }
        }
        
        function checkVideo() {
            log('开始检查视频元素...');
            
            const videos = document.querySelectorAll('video');
            log(`找到 ${videos.length} 个video元素`);
            
            if (videos.length > 0) {
                updateStatus('video-status', `找到 ${videos.length} 个视频元素`, 'success');
                
                videos.forEach((video, index) => {
                    log(`Video ${index + 1}:`);
                    log(`  - src: ${video.src || video.currentSrc || '无'}`);
                    log(`  - 尺寸: ${video.offsetWidth}x${video.offsetHeight}`);
                    log(`  - 可见: ${video.offsetWidth > 0 && video.offsetHeight > 0 ? '是' : '否'}`);
                    log(`  - 当前时间: ${video.currentTime.toFixed(2)}s`);
                    log(`  - 总时长: ${video.duration ? video.duration.toFixed(2) + 's' : '未知'}`);
                    log(`  - 播放倍速: ${video.playbackRate}x`);
                    log(`  - 暂停状态: ${video.paused ? '是' : '否'}`);
                });
            } else {
                updateStatus('video-status', '未找到视频元素', 'error');
                log('✗ 未找到任何video元素', 'error');
                
                // 检查可能的视频容器
                const containers = [
                    '.bpx-player-video-wrap',
                    '.bilibili-player-video',
                    '[data-video]',
                    '.player-wrap',
                    '#bilibili-player'
                ];
                
                containers.forEach(selector => {
                    const container = document.querySelector(selector);
                    if (container) {
                        log(`✓ 找到视频容器: ${selector}`, 'info');
                    }
                });
            }
        }
        
        function testPopupMessage() {
            log('测试Popup消息发送...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                const message = {action: 'setPlaybackRate', rate: 1.5};
                log(`发送消息: ${JSON.stringify(message)}`);
                
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`✗ 消息发送失败: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        log(`✓ 收到响应: ${JSON.stringify(response)}`, 'success');
                    }
                });
            } else {
                log('✗ Chrome扩展API不可用', 'error');
            }
        }
        
        function testDirectFunction() {
            log('测试直接函数调用...');
            
            // 尝试调用content script中的函数
            if (typeof window.testPlaybackRate === 'function') {
                log('调用 window.testPlaybackRate(1.5)...');
                const result = window.testPlaybackRate(1.5);
                log(`函数返回: ${result}`, result ? 'success' : 'error');
            } else {
                log('✗ window.testPlaybackRate 函数不存在', 'error');
            }
            
            if (typeof window.testVideo === 'function') {
                log('调用 window.testVideo()...');
                window.testVideo();
            } else {
                log('✗ window.testVideo 函数不存在', 'error');
            }
        }
        
        function injectTestCode() {
            log('注入测试代码...');
            
            const testCode = `
                // 测试倍速设置
                function testPlaybackRateManual(rate) {
                    console.log('手动测试倍速设置:', rate);
                    const videos = document.querySelectorAll('video');
                    if (videos.length > 0) {
                        const video = videos[0];
                        const oldRate = video.playbackRate;
                        video.playbackRate = rate;
                        console.log('倍速设置:', oldRate, '->', video.playbackRate);
                        return true;
                    }
                    return false;
                }
                
                // 测试通知显示
                function testNotification(message) {
                    const notification = document.createElement('div');
                    notification.style.cssText = \`
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #4CAF50;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 8px;
                        z-index: 10000;
                        font-size: 14px;
                    \`;
                    notification.textContent = message;
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 3000);
                }
                
                // 立即测试
                console.log('=== 手动测试开始 ===');
                testNotification('测试通知显示');
                testPlaybackRateManual(1.5);
                console.log('=== 手动测试结束 ===');
            `;
            
            try {
                eval(testCode);
                log('✓ 测试代码注入成功', 'success');
            } catch (error) {
                log(`✗ 测试代码注入失败: ${error}`, 'error');
            }
        }
        
        function clearLog() {
            logDiv.innerHTML = '';
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            log('页面加载完成，开始自动诊断...');
            setTimeout(() => {
                checkExtension();
                checkPage();
                checkVideo();
            }, 1000);
        };
        
        // 监听控制台消息
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args.length > 0 && typeof args[0] === 'string') {
                log(`Console: ${args.join(' ')}`);
            }
        };
    </script>
</body>
</html>
