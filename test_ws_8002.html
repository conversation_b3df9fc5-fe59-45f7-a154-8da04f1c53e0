<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 8002端口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>WebSocket 8002端口连接测试</h1>
    
    <div>
        <button onclick="connectWebSocket()">连接 ws://localhost:8002/scp</button>
        <button onclick="sendTestMessage()">发送测试消息</button>
        <button onclick="sendPlaybackRate()">发送倍速命令</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="log"></div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log ${type}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已连接', 'info');
                return;
            }
            
            const url = 'ws://localhost:8002/scp';
            log(`尝试连接: ${url}`, 'info');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket连接成功!', 'success');
                    log(`连接地址: ${ws.url}`, 'info');
                };
                
                ws.onmessage = function(event) {
                    log(`📨 收到消息: ${event.data}`, 'success');
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`, 'error');
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket连接关闭: code=${event.code}, reason=${event.reason}`, 'error');
                    ws = null;
                };
                
            } catch (error) {
                log(`创建WebSocket失败: ${error}`, 'error');
            }
        }
        
        function sendTestMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }
            
            const testMsg = {
                type: 'test',
                message: 'Hello from test page',
                timestamp: Date.now()
            };
            
            try {
                ws.send(JSON.stringify(testMsg));
                log(`发送测试消息: ${JSON.stringify(testMsg)}`, 'info');
            } catch (error) {
                log(`发送消息失败: ${error}`, 'error');
            }
        }
        
        function sendPlaybackRate() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }
            
            const rateMsg = {
                command: 'setPlaybackRate',
                value: 1.5
            };
            
            try {
                ws.send(JSON.stringify(rateMsg));
                log(`发送倍速命令: ${JSON.stringify(rateMsg)}`, 'info');
            } catch (error) {
                log(`发送命令失败: ${error}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动尝试连接
        window.onload = function() {
            log('页面加载完成，准备测试WebSocket连接', 'info');
            setTimeout(connectWebSocket, 1000);
        };
    </script>
</body>
</html>
