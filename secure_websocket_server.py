#!/usr/bin/env python3
"""
安全的WebSocket服务器 - 支持HTTPS和WSS
用于Safari扩展连接
"""

import asyncio
import websockets
import json
import ssl
import logging
from pathlib import Path
import subprocess
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecureWebSocketServer:
    def __init__(self, host='localhost', port=8443):
        self.host = host
        self.port = port
        self.clients = set()
        self.ssl_context = None
        
    def create_self_signed_cert(self):
        """创建自签名证书"""
        cert_file = 'server.crt'
        key_file = 'server.key'
        
        if not os.path.exists(cert_file) or not os.path.exists(key_file):
            logger.info("创建自签名证书...")
            
            # 创建自签名证书
            cmd = [
                'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-keyout', key_file,
                '-out', cert_file, '-days', '365', '-nodes', '-subj',
                '/C=US/ST=State/L=City/O=Organization/CN=localhost'
            ]
            
            try:
                subprocess.run(cmd, check=True, capture_output=True)
                logger.info(f"证书已创建: {cert_file}, {key_file}")
            except subprocess.CalledProcessError as e:
                logger.error(f"创建证书失败: {e}")
                return False
                
        return True
    
    def setup_ssl(self):
        """设置SSL上下文"""
        if not self.create_self_signed_cert():
            return False
            
        self.ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        try:
            self.ssl_context.load_cert_chain('server.crt', 'server.key')
            logger.info("SSL证书加载成功")
            return True
        except Exception as e:
            logger.error(f"SSL设置失败: {e}")
            return False
    
    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"客户端已连接: {client_info} (总计: {len(self.clients)})")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        logger.info(f"客户端已断开 (剩余: {len(self.clients)})")
    
    async def handle_message(self, websocket, message):
        """处理收到的消息"""
        try:
            data = json.loads(message)
            logger.info(f"收到消息: {data}")
            
            # 回复确认消息
            response = {
                "status": "received",
                "original": data,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await websocket.send(json.dumps(response))
            logger.info(f"已回复: {response}")
            
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
            error_response = {"error": "Invalid JSON format"}
            await websocket.send(json.dumps(error_response))
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    async def client_handler(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        
        try:
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("客户端连接正常关闭")
        except Exception as e:
            logger.error(f"客户端处理错误: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def broadcast_message(self, message):
        """向所有客户端广播消息"""
        if self.clients:
            logger.info(f"广播消息给 {len(self.clients)} 个客户端: {message}")
            await asyncio.gather(
                *[client.send(json.dumps(message)) for client in self.clients],
                return_exceptions=True
            )
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"启动安全WebSocket服务器...")
        logger.info(f"地址: wss://{self.host}:{self.port}/scp")
        
        if not self.setup_ssl():
            logger.error("SSL设置失败，无法启动安全服务器")
            return
        
        try:
            server = await websockets.serve(
                self.client_handler,
                self.host,
                self.port,
                ssl=self.ssl_context,
                subprotocols=['scp']
            )
            
            logger.info("✓ 安全WebSocket服务器已启动")
            logger.info("注意: 首次连接时浏览器可能会警告证书不受信任，请选择继续")
            
            await server.wait_closed()
            
        except Exception as e:
            logger.error(f"服务器启动失败: {e}")

async def send_test_commands(server):
    """发送测试命令"""
    await asyncio.sleep(5)  # 等待客户端连接
    
    test_commands = [
        {"command": "setPlaybackRate", "value": 1.5},
        {"command": "setPlaybackRate", "value": 2.0},
        {"command": "pause"},
        {"command": "play"},
        {"command": "setPlaybackRate", "value": 1.0}
    ]
    
    for cmd in test_commands:
        logger.info(f"发送测试命令: {cmd}")
        await server.broadcast_message(cmd)
        await asyncio.sleep(3)

async def main():
    """主函数"""
    server = SecureWebSocketServer()
    
    # 启动服务器和测试命令发送
    await asyncio.gather(
        server.start_server(),
        send_test_commands(server)
    )

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器已停止")
