<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试 - 现有服务器</title>
    <style>
        body {
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2d2d2d;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #444;
        }
        
        .header h1 {
            color: #61dafb;
            margin: 0;
            font-size: 2.2em;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #333;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .status-indicator.connecting {
            background: #ffc107;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .log-container {
            background: #1e1e1e;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #333;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .timestamp {
            color: #888;
            font-size: 11px;
        }
        
        .log-info { color: #61dafb; }
        .log-success { color: #28a745; }
        .log-warning { color: #ffc107; }
        .log-error { color: #dc3545; }
        .log-data { color: #f8f9fa; font-weight: bold; }
        
        .url-input {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #444;
            color: #e0e0e0;
            font-family: monospace;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #61dafb;
            display: block;
        }
        
        .stat-label {
            color: #aaa;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 WebSocket连接测试</h1>
            <p>连接到现有服务器进行测试</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <span id="statusIndicator" class="status-indicator"></span>
                <span id="statusText">未连接</span>
            </div>
            <div class="status-item">
                <strong>服务器:</strong>
                <input type="text" id="serverUrl" class="url-input" value="ws://localhost:8002/scp" placeholder="WebSocket服务器地址">
            </div>
            <div class="status-item">
                <strong>消息数:</strong>
                <span id="messageCount">0</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" id="connectBtn" onclick="connectWebSocket()">🔌 连接</button>
            <button class="btn danger" id="disconnectBtn" onclick="disconnectWebSocket()" disabled>🔌 断开</button>
            <button class="btn success" onclick="sendTestMessage()">📤 发送测试</button>
            <button class="btn" onclick="sendPlaybackRate()">🎵 发送倍速命令</button>
            <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">
                <span class="timestamp">[启动]</span> WebSocket测试页面已加载，点击"连接"开始测试
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalMessages">0</span>
                <div class="stat-label">总消息</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="connectTime">--</span>
                <div class="stat-label">连接时间</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="dataReceived">0</span>
                <div class="stat-label">接收字节</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="lastLatency">--</span>
                <div class="stat-label">延迟(ms)</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let totalDataReceived = 0;
        let connectStartTime = null;
        
        const logContainer = document.getElementById('logContainer');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        
        function log(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            let content = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            
            if (data) {
                content += `\n<pre style="margin: 5px 0; padding: 10px; background: #333; border-radius: 4px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            logEntry.innerHTML = content;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[WebSocket测试] ${message}`, data || '');
        }
        
        function updateStatus(status, indicator = 'disconnected') {
            statusText.textContent = status;
            statusIndicator.className = `status-indicator ${indicator}`;
            
            connectBtn.disabled = indicator === 'connected' || indicator === 'connecting';
            disconnectBtn.disabled = indicator === 'disconnected';
        }
        
        function updateStats() {
            document.getElementById('totalMessages').textContent = messageCount;
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('dataReceived').textContent = totalDataReceived;
        }
        
        function connectWebSocket() {
            const url = document.getElementById('serverUrl').value.trim();
            
            if (!url) {
                log('请输入WebSocket服务器地址', 'error');
                return;
            }
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接', 'warning');
                return;
            }
            
            log(`开始连接WebSocket: ${url}`, 'info');
            updateStatus('连接中...', 'connecting');
            connectStartTime = Date.now();
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    const connectTime = Date.now() - connectStartTime;
                    log(`✅ WebSocket连接成功! (耗时: ${connectTime}ms)`, 'success');
                    updateStatus('已连接', 'connected');
                    document.getElementById('connectTime').textContent = `${connectTime}ms`;
                    
                    // 发送连接确认消息
                    const welcomeMsg = {
                        type: 'client_connected',
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        url: window.location.href,
                        testPage: true
                    };
                    
                    try {
                        ws.send(JSON.stringify(welcomeMsg));
                        log('发送连接确认消息', 'info', welcomeMsg);
                    } catch (error) {
                        log('发送连接确认消息失败', 'error', error);
                    }
                };
                
                ws.onmessage = function(event) {
                    messageCount++;
                    const dataSize = new Blob([event.data]).size;
                    totalDataReceived += dataSize;
                    
                    log('📨 收到WebSocket消息', 'data');
                    log(`原始数据 (${dataSize} 字节): ${event.data}`, 'info');
                    
                    try {
                        const data = JSON.parse(event.data);
                        log('✅ JSON解析成功', 'success', data);
                        
                        // 计算延迟
                        if (data.timestamp) {
                            const latency = Date.now() - data.timestamp;
                            document.getElementById('lastLatency').textContent = `${latency}ms`;
                            log(`⏱️ 消息延迟: ${latency}ms`, 'info');
                        }
                        
                        // 检查消息类型
                        if (data.command) {
                            log(`🎯 收到命令: ${data.command}`, 'success');
                            if (data.command === 'setPlaybackRate') {
                                log(`🎵 倍速命令: ${data.value}x`, 'success');
                            }
                        }
                        
                        if (data.type === 'welcome') {
                            log('🎉 收到服务器欢迎消息', 'success');
                        }
                        
                    } catch (error) {
                        log('❌ JSON解析失败', 'error', {
                            error: error.message,
                            data: event.data
                        });
                    }
                    
                    updateStats();
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket连接错误', 'error', error);
                    updateStatus('连接错误', 'disconnected');
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket连接已关闭`, 'warning', {
                        code: event.code,
                        reason: event.reason,
                        wasClean: event.wasClean
                    });
                    updateStatus('已断开', 'disconnected');
                    document.getElementById('connectTime').textContent = '--';
                    ws = null;
                };
                
            } catch (error) {
                log('❌ 创建WebSocket失败', 'error', error);
                updateStatus('连接失败', 'disconnected');
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                log('主动断开WebSocket连接', 'warning');
                ws.close();
            } else {
                log('WebSocket未连接', 'warning');
            }
        }
        
        function sendTestMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接，无法发送消息', 'error');
                return;
            }
            
            const testMsg = {
                type: 'test_message',
                timestamp: Date.now(),
                message: 'Hello from test page!',
                random: Math.random().toString(36).substr(2, 9)
            };
            
            try {
                ws.send(JSON.stringify(testMsg));
                log('📤 发送测试消息', 'success', testMsg);
            } catch (error) {
                log('❌ 发送测试消息失败', 'error', error);
            }
        }
        
        function sendPlaybackRate() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接，无法发送消息', 'error');
                return;
            }
            
            const rates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
            const randomRate = rates[Math.floor(Math.random() * rates.length)];
            
            const rateMsg = {
                command: 'setPlaybackRate',
                value: randomRate,
                timestamp: Date.now(),
                source: 'test_page'
            };
            
            try {
                ws.send(JSON.stringify(rateMsg));
                log(`📤 发送倍速命令: ${randomRate}x`, 'success', rateMsg);
            } catch (error) {
                log('❌ 发送倍速命令失败', 'error', error);
            }
        }
        
        function clearLog() {
            logContainer.innerHTML = '';
            log('日志已清空', 'info');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('🎯 WebSocket测试页面已加载', 'success');
            log('输入服务器地址并点击"连接"开始测试', 'info');
            
            // 检测当前页面是否在bilibili域名下
            if (window.location.hostname.includes('bilibili')) {
                log('🎬 检测到bilibili页面环境', 'info');
            }
        });
        
        // 页面卸载时清理连接
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey || event.metaKey) {
                switch(event.key) {
                    case 'Enter':
                        event.preventDefault();
                        connectWebSocket();
                        break;
                    case 'Escape':
                        event.preventDefault();
                        disconnectWebSocket();
                        break;
                }
            }
        });
    </script>
</body>
</html>
