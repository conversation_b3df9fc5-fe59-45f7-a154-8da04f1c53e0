// Safari扩展后台脚本 - WebSocket连接处理
console.log('=== Bilibili 控制插件后台脚本已启动 ===');

let ws = null;

// WebSocket连接函数
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已连接');
    return;
  }

  console.log('=== 后台脚本WebSocket连接 ===');
  const url = 'ws://127.0.0.1:8002/scp';
  console.log('连接地址:', url);

  try {
    ws = new WebSocket(url);
    console.log('后台WebSocket对象已创建');

    ws.onopen = function() {
      console.log('✅ 后台WebSocket连接成功!');
      
      // 通知所有content scripts连接成功
      chrome.tabs.query({url: "https://www.bilibili.com/*"}, (tabs) => {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            type: 'websocket_connected',
            message: 'WebSocket连接成功'
          }).catch(() => {
            // 忽略发送失败的错误
          });
        });
      });
    };

    ws.onmessage = function(event) {
      console.log('📨 后台收到消息:', event.data);
      
      try {
        const data = JSON.parse(event.data);
        console.log('解析后的数据:', data);
        
        // 转发消息给content scripts
        if (data.command) {
          chrome.tabs.query({url: "https://www.bilibili.com/*"}, (tabs) => {
            tabs.forEach(tab => {
              chrome.tabs.sendMessage(tab.id, {
                type: 'websocket_command',
                command: data.command,
                value: data.value
              }).catch(() => {
                // 忽略发送失败的错误
              });
            });
          });
        }
      } catch (error) {
        console.log('消息不是JSON格式:', event.data);
      }
    };

    ws.onerror = function(error) {
      console.error('❌ 后台WebSocket错误:', error);
    };

    ws.onclose = function(event) {
      console.log('🔌 后台WebSocket连接关闭:', event.code, event.reason);
      ws = null;
    };

  } catch (error) {
    console.error('创建后台WebSocket失败:', error);
  }
}

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('后台收到消息:', request);
  
  if (request.action === 'connect_websocket') {
    connectWebSocket();
    sendResponse({success: true});
  }
  
  if (request.action === 'send_websocket_message') {
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(request.message));
        console.log('后台发送消息:', request.message);
        sendResponse({success: true});
      } catch (error) {
        console.error('后台发送消息失败:', error);
        sendResponse({success: false, error: error.message});
      }
    } else {
      console.log('WebSocket未连接');
      sendResponse({success: false, error: 'WebSocket未连接'});
    }
  }
  
  if (request.action === 'get_websocket_status') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    sendResponse({connected: connected});
  }
  
  return true; // 保持消息通道开放
});

// 启动时尝试连接
setTimeout(() => {
  console.log('后台脚本启动WebSocket连接...');
  connectWebSocket();
}, 2000);

console.log('=== 后台脚本初始化完成 ===');
