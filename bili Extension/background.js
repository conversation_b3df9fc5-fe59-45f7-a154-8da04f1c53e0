// Safari扩展后台脚本 - WebSocket连接处理
console.log('=== Bilibili 控制插件后台脚本已启动 ===');
console.log('当前时间:', new Date().toISOString());
console.log('WebSocket构造函数:', typeof WebSocket);
console.log('chrome.runtime:', typeof chrome.runtime);
console.log('chrome.tabs:', typeof chrome.tabs);

let ws = null;

// WebSocket连接函数（基于测试页面的成功代码）
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已连接');
    return;
  }

  const url = 'ws://localhost:8002/scp';
  console.log('=== 后台脚本WebSocket连接 ===');
  console.log('连接地址:', url);
  console.log('WebSocket构造函数类型:', typeof WebSocket);

  if (!url) {
    console.error('WebSocket地址为空');
    return;
  }

  console.log('开始连接WebSocket...');
  const connectStartTime = Date.now();

  try {
    ws = new WebSocket(url);
    console.log('✓ WebSocket对象已创建');
    console.log('初始状态:', ws.readyState);

    ws.onopen = function(event) {
      const connectTime = Date.now() - connectStartTime;
      console.log(`✅ 后台WebSocket连接成功! (耗时: ${connectTime}ms)`);
      console.log('连接事件:', event);
      console.log('WebSocket状态:', ws.readyState);
      console.log('WebSocket URL:', ws.url);
      console.log('WebSocket协议:', ws.protocol);
      console.log('WebSocket扩展:', ws.extensions);

      // 发送连接确认消息（模仿测试页面）
      const welcomeMsg = {
        type: 'safari_plugin_connected',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        source: 'safari_extension_background',
        connectTime: connectTime
      };

      try {
        ws.send(JSON.stringify(welcomeMsg));
        console.log('✓ 发送连接确认消息:', welcomeMsg);
      } catch (error) {
        console.error('❌ 发送连接确认消息失败:', error);
      }

      // 通知所有content scripts连接成功
      chrome.tabs.query({url: "https://www.bilibili.com/*"}, (tabs) => {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            type: 'websocket_connected',
            message: 'WebSocket连接成功',
            connectTime: connectTime
          }).catch(() => {
            // 忽略发送失败的错误
          });
        });
      });
    };

    ws.onmessage = function(event) {
      console.log('📨 后台收到消息:', event.data);
      
      try {
        const data = JSON.parse(event.data);
        console.log('解析后的数据:', data);
        
        // 转发消息给content scripts
        if (data.command) {
          chrome.tabs.query({url: "https://www.bilibili.com/*"}, (tabs) => {
            tabs.forEach(tab => {
              chrome.tabs.sendMessage(tab.id, {
                type: 'websocket_command',
                command: data.command,
                value: data.value
              }).catch(() => {
                // 忽略发送失败的错误
              });
            });
          });
        }
      } catch (error) {
        console.log('消息不是JSON格式:', event.data);
      }
    };

    ws.onerror = function(error) {
      console.error('❌ 后台WebSocket错误详情:');
      console.error('- 错误事件:', error);
      console.error('- 错误类型:', error.type);
      console.error('- 错误目标:', error.target);
      console.error('- WebSocket状态:', ws ? ws.readyState : 'undefined');
      console.error('- WebSocket URL:', ws ? ws.url : 'undefined');
      console.error('- 时间戳:', new Date().toISOString());
    };

    ws.onclose = function(event) {
      console.log('🔌 后台WebSocket连接关闭详情:');
      console.log('- 关闭代码:', event.code);
      console.log('- 关闭原因:', event.reason);
      console.log('- 是否干净关闭:', event.wasClean);
      console.log('- 事件类型:', event.type);
      console.log('- 时间戳:', new Date().toISOString());

      // 解释关闭代码
      let closeReason = '';
      switch(event.code) {
        case 1000: closeReason = '正常关闭'; break;
        case 1001: closeReason = '端点离开'; break;
        case 1002: closeReason = '协议错误'; break;
        case 1003: closeReason = '不支持的数据'; break;
        case 1006: closeReason = '连接异常关闭'; break;
        case 1011: closeReason = '服务器错误'; break;
        default: closeReason = `未知错误 (${event.code})`;
      }
      console.log('关闭原因解释:', closeReason);

      ws = null;
    };

  } catch (error) {
    console.error('创建后台WebSocket失败:', error);
  }
}

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('后台收到消息:', request);
  
  if (request.action === 'connect_websocket') {
    connectWebSocket();
    sendResponse({success: true});
  }
  
  if (request.action === 'send_websocket_message') {
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(request.message));
        console.log('后台发送消息:', request.message);
        sendResponse({success: true});
      } catch (error) {
        console.error('后台发送消息失败:', error);
        sendResponse({success: false, error: error.message});
      }
    } else {
      console.log('WebSocket未连接');
      sendResponse({success: false, error: 'WebSocket未连接'});
    }
  }
  
  if (request.action === 'get_websocket_status') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    sendResponse({connected: connected});
  }
  
  return true; // 保持消息通道开放
});

// 启动时尝试连接
setTimeout(() => {
  console.log('后台脚本启动WebSocket连接...');
  connectWebSocket();
}, 2000);

console.log('=== 后台脚本初始化完成 ===');
