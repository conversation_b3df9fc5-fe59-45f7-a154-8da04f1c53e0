// 简化的WebSocket连接变量
let ws = null;

// 倍速播放
function setPlaybackRate(rate, source = 'unknown') {
  console.log(`=== setPlaybackRate 被调用 ===`);
  console.log(`参数: rate=${rate}, source=${source}`);

  // 尝试多种方式查找视频元素
  let video = document.querySelector('video');

  // 如果没找到，尝试查找bilibili特定的视频容器
  if (!video) {
    video = document.querySelector('.bpx-player-video-wrap video');
  }
  if (!video) {
    video = document.querySelector('.bilibili-player-video video');
  }
  if (!video) {
    video = document.querySelector('[data-video] video');
  }
  if (!video) {
    // 查找所有video元素，选择第一个可见的
    const videos = document.querySelectorAll('video');
    for (let v of videos) {
      if (v.offsetWidth > 0 && v.offsetHeight > 0) {
        video = v;
        break;
      }
    }
  }

  console.log('找到的视频元素:', video);

  if (video) {
    try {
      // 确保倍速值在合理范围内
      const playbackRate = Math.max(0.25, Math.min(4, parseFloat(rate)));

      console.log(`设置倍速: ${video.playbackRate} -> ${playbackRate}`);
      video.playbackRate = playbackRate;

      console.log(`视频倍速已设置为: ${playbackRate}x`);
      showNotification(`倍速已调整为 ${playbackRate}x (${source})`, 'success');

      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      showNotification('设置倍速失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 跳过片头（假设片头为前60秒）
function skipIntro() {
  const video = document.querySelector('video');
  if (video && video.currentTime < 60) {
    video.currentTime = 60;
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 999999;
    font-size: 14px;
    font-weight: bold;
    color: white;
    ${type === 'success' ? 'background: #4CAF50;' :
      type === 'error' ? 'background: #f44336;' :
      type === 'warning' ? 'background: #ff9800;' :
      'background: #2196F3;'}
  `;
  notification.textContent = message;

  document.body.appendChild(notification);

  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 简化的WebSocket连接函数
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已连接');
    return;
  }

  console.log('=== 开始简化WebSocket连接 ===');
  console.log('当前时间:', new Date().toLocaleTimeString());
  console.log('页面URL:', window.location.href);

  const url = 'ws://localhost:8003/scp';
  console.log('连接地址:', url);

  try {
    ws = new WebSocket(url);
    console.log('WebSocket对象已创建');

    ws.onopen = function(event) {
      console.log('✅ WebSocket连接成功!', event);
      console.log('连接地址:', ws.url);

      // 发送简单的连接确认
      const msg = {
        type: 'safari_plugin_connected',
        timestamp: Date.now(),
        url: window.location.href
      };

      try {
        ws.send(JSON.stringify(msg));
        console.log('发送连接确认:', msg);
      } catch (error) {
        console.error('发送消息失败:', error);
      }
    };

    ws.onmessage = function(event) {
      console.log('📨 收到消息:', event.data);

      try {
        const data = JSON.parse(event.data);
        console.log('解析后的数据:', data);

        if (data.command === 'setPlaybackRate') {
          console.log('🎵 收到倍速命令:', data.value);
          setPlaybackRate(data.value);
        }
      } catch (error) {
        console.log('消息不是JSON格式:', event.data);
      }
    };

    ws.onerror = function(error) {
      console.error('❌ WebSocket错误:', error);
    };

    ws.onclose = function(event) {
      console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
      ws = null;

      // 5秒后重试
      setTimeout(() => {
        console.log('5秒后重试连接...');
        connectWebSocket();
      }, 5000);
    };

  } catch (error) {
    console.error('创建WebSocket失败:', error);
  }
}

function connectToUrl(urls, index) {
  if (index >= urls.length) {
    console.error('所有WebSocket地址都连接失败');
    console.error('尝试过的地址:', urls);
    handleConnectionFailure();
    return;
  }

  const url = urls[index];
  console.log(`尝试连接WebSocket (${index + 1}/${urls.length}): ${url}`);

  // 检查WebSocket支持
  if (typeof WebSocket === 'undefined') {
    console.error('WebSocket不被支持');
    handleConnectionFailure();
    return;
  }

  // 设置连接超时
  let connectionTimeout = setTimeout(() => {
    console.error(`WebSocket连接超时 (${url})`);
    if (ws && ws.readyState === WebSocket.CONNECTING) {
      ws.close();
    }
    // 尝试下一个URL
    setTimeout(() => {
      connectToUrl(urls, index + 1);
    }, 1000);
  }, 5000); // 5秒超时

  try {
    console.log('创建WebSocket对象...');
    ws = new WebSocket(url);
    console.log('WebSocket对象已创建:', ws);
    console.log('WebSocket readyState:', ws.readyState);
    console.log('WebSocket URL:', ws.url);
    console.log('WebSocket protocol:', ws.protocol);
    console.log('WebSocket extensions:', ws.extensions);

    ws.onopen = function(event) {
      console.log('=== WebSocket连接已建立 ===');
      console.log('✓ 连接成功!', event);
      console.log('连接地址:', ws.url);
      console.log('连接时间:', new Date().toLocaleTimeString());

      // 清除连接超时
      if (connectionTimeout) {
        clearTimeout(connectionTimeout);
        connectionTimeout = null;
      }

      // 标记连接成功，防止重复连接
      isConnected = true;
      connectionAttempted = false;

      const protocol = ws.url.startsWith('wss:') ? 'WSS' : 'WS';
      showNotification(`${protocol} WebSocket连接成功`, 'success');

      // 发送连接确认消息
      try {
        const connectMsg = {
          type: 'client_connected',
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: Date.now()
        };
        ws.send(JSON.stringify(connectMsg));
        console.log('发送连接确认消息:', connectMsg);
      } catch (error) {
        console.error('发送连接确认消息失败:', error);
      }

      // 清除重连定时器
      if (reconnectInterval) {
        clearInterval(reconnectInterval);
        reconnectInterval = null;
        console.log('已停止自动重连机制');
      }

      console.log('WebSocket连接初始化完成，不再重复连接');
    };

    ws.onmessage = function(event) {
      console.log('收到WebSocket消息:', event.data);

      try {
        const data = JSON.parse(event.data);
        console.log('解析后的消息:', data);

        // 处理新的消息格式 {"command":"setPlaybackRate","value":2}
        if (data.command === 'setPlaybackRate' && data.value !== undefined) {
          console.log(`WebSocket请求设置倍速为: ${data.value}x`);
          console.log('调用setPlaybackRate函数...');

          const result = setPlaybackRate(data.value, 'websocket');
          console.log(`setPlaybackRate函数返回: ${result}`);

          if (result) {
            console.log('WebSocket倍速设置成功');
          } else {
            console.log('WebSocket倍速设置失败');
          }
        }

        // 处理其他命令
        if (data.command === 'skipIntro') {
          skipIntro();
          showNotification('已跳过片头', 'success');
        }

      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    ws.onerror = function(error) {
      console.error(`✗ WebSocket连接错误 (${url}):`, error);
      console.error('错误时间:', new Date().toLocaleTimeString());
      console.error('错误详情:', {
        type: error.type,
        target: error.target,
        readyState: ws ? ws.readyState : 'undefined'
      });

      // 尝试下一个URL
      setTimeout(() => {
        connectToUrl(urls, index + 1);
      }, 1000);
    };

    ws.onclose = function(event) {
      console.log(`WebSocket连接已关闭 (${url})`, {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });

      // 重置连接状态
      ws = null;
      isConnected = false;
      connectionAttempted = false;

      // 如果不是正常关闭，尝试下一个URL或重连
      if (!event.wasClean) {
        if (index + 1 < urls.length) {
          console.log('尝试下一个WebSocket地址...');
          setTimeout(() => {
            connectToUrl(urls, index + 1);
          }, 1000);
        } else {
          console.log('所有地址都失败，启动重连机制...');
          handleConnectionFailure();
        }
      } else {
        console.log('WebSocket正常关闭，不再重连');
      }
    };

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    handleConnectionFailure();
  }
}

function handleConnectionFailure() {
  console.log('处理WebSocket连接失败...');

  // 重置连接状态
  connectionAttempted = false;

  showNotification('WebSocket连接失败，将自动重试', 'error');

  // 启动重连机制（只有在未连接且没有重连定时器时）
  if (!isConnected && !reconnectInterval) {
    console.log('启动自动重连机制...');
    reconnectInterval = setInterval(() => {
      // 只有在未连接时才重连
      if (!isConnected) {
        console.log('尝试重新连接WebSocket...');
        connectWebSocket();
      } else {
        console.log('WebSocket已连接，停止重连机制');
        clearInterval(reconnectInterval);
        reconnectInterval = null;
      }
    }, 10000); // 10秒重试一次
  }
}

// 监听来自 popup 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'setPlaybackRate') {
    console.log(`popup请求设置倍速为: ${request.rate}x`);
    if (setPlaybackRate(request.rate, 'popup')) {
      sendResponse({status: 'ok', message: `倍速设置为 ${request.rate}x`});
    } else {
      sendResponse({status: 'error', message: '设置倍速失败'});
    }
  }
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({status: 'ok'});
  }
});

// 添加测试函数
window.testPlaybackRate = function(rate = 1.5) {
  console.log(`手动测试倍速设置: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

window.testWebSocket = function() {
  console.log('=== WebSocket连接测试 ===');
  console.log('连接状态标志 isConnected:', isConnected);
  console.log('连接尝试标志 connectionAttempted:', connectionAttempted);
  console.log('重连定时器:', reconnectInterval ? '运行中' : '未运行');
  console.log('ws对象:', ws);

  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);

    if (ws.readyState === WebSocket.OPEN && isConnected) {
      console.log('✓ WebSocket连接正常');

      // 发送测试消息
      try {
        const testMsg = JSON.stringify({command: 'test', timestamp: Date.now()});
        ws.send(testMsg);
        console.log('发送测试消息:', testMsg);
      } catch (error) {
        console.error('发送测试消息失败:', error);
      }

      return true;
    } else {
      console.log('✗ WebSocket未连接或状态异常');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    return false;
  }
};

// 初始化
console.log('=== Bilibili 控制插件已加载 ===');
console.log('当前页面URL:', window.location.href);
console.log('当前时间:', new Date().toLocaleTimeString());

// 显示加载通知
showNotification('🎯 Bilibili控制插件已加载', 'success');

// 测试网络连接
function testNetworkConnectivity() {
  console.log('=== 测试网络连接 ===');

  // 测试基本的HTTP请求
  fetch('http://localhost:8003/')
    .then(response => {
      console.log('✓ HTTP连接测试成功:', response.status);
      return response.text();
    })
    .then(data => {
      console.log('HTTP响应数据:', data.substring(0, 100));
    })
    .catch(error => {
      console.error('✗ HTTP连接测试失败:', error);
      console.error('尝试8002端口...');
      return fetch('http://localhost:8002/');
    })
    .then(response => {
      if (response) {
        console.log('✓ HTTP 8002连接测试成功:', response.status);
      }
    })
    .catch(error => {
      console.error('✗ HTTP 8002连接测试也失败:', error);
    });
}

// 启动WebSocket连接
setTimeout(() => {
  console.log('启动WebSocket连接...');
  console.log('检查全局变量状态:');
  console.log('- isConnected:', typeof isConnected !== 'undefined' ? isConnected : 'undefined');
  console.log('- connectionAttempted:', typeof connectionAttempted !== 'undefined' ? connectionAttempted : 'undefined');
  console.log('- ws:', typeof ws !== 'undefined' ? ws : 'undefined');
  console.log('- reconnectInterval:', typeof reconnectInterval !== 'undefined' ? reconnectInterval : 'undefined');

  // 先测试网络连接
  testNetworkConnectivity();

  try {
    connectWebSocket();
  } catch (error) {
    console.error('调用connectWebSocket时出错:', error);
    console.error('错误堆栈:', error.stack);
  }
}, 2000);