document.getElementById('speed15').addEventListener('click', () => {
  console.log('点击1.5倍速按钮');
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    if (tabs[0]) {
      console.log('发送倍速消息到tab:', tabs[0].id);
      chrome.tabs.sendMessage(tabs[0].id, {action: 'setPlaybackRate', rate: 1.5}, (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
        } else {
          console.log('收到响应:', response);
        }
      });
    } else {
      console.error('未找到活动标签页');
    }
  });
});

document.getElementById('skipIntro').addEventListener('click', () => {
  console.log('点击跳过片头按钮');
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    if (tabs[0]) {
      console.log('发送跳过片头消息到tab:', tabs[0].id);
      chrome.tabs.sendMessage(tabs[0].id, {action: 'skipIntro'}, (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
        } else {
          console.log('收到响应:', response);
        }
      });
    } else {
      console.error('未找到活动标签页');
    }
  });
});