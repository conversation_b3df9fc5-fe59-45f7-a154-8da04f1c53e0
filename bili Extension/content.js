// 简化的Safari插件 - Bilibili控制
console.log('=== Bilibili 控制插件已加载 (简化版) ===');

// WebSocket连接变量
let ws = null;

// 设置视频倍速
function setPlaybackRate(rate, source = 'manual') {
  console.log(`设置倍速为 ${rate}x (来源: ${source})`);
  
  const video = document.querySelector('video');
  if (video) {
    try {
      video.playbackRate = rate;
      console.log(`✅ 倍速设置成功: ${rate}x`);
      showNotification(`倍速已设置为 ${rate}x`, 'success');
      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      return false;
    }
  } else {
    console.log('❌ 未找到视频元素');
    return false;
  }
}

// 跳过片头
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    video.currentTime = 60;
    console.log('已跳过片头');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  console.log(`通知 [${type}]: ${message}`);
  
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 简化的WebSocket连接函数
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已连接');
    return;
  }

  console.log('=== 开始简化WebSocket连接 ===');
  console.log('当前页面URL:', window.location.href);
  console.log('User Agent:', navigator.userAgent);
  console.log('WebSocket支持:', typeof WebSocket !== 'undefined');

  const url = 'ws://localhost:8002/scp';
  console.log('连接地址:', url);

  // 检查WebSocket支持
  if (typeof WebSocket === 'undefined') {
    console.error('❌ WebSocket不被支持');
    showNotification('WebSocket不被支持', 'error');
    return;
  }

  try {
    console.log('🔄 创建WebSocket连接...');

    // 确保URL格式正确
    console.log('URL检查:', url);
    console.log('URL类型:', typeof url);

    // 创建WebSocket时添加错误处理
    ws = new WebSocket(url);
    console.log('✓ WebSocket对象已创建');
    console.log('WebSocket readyState:', ws.readyState);
    console.log('WebSocket URL:', ws.url);

    // 设置事件处理器
    ws.addEventListener('open', function(event) {
      console.log('✅ WebSocket连接成功!', event);
      console.log('连接详情:', {
        url: ws.url,
        readyState: ws.readyState,
        protocol: ws.protocol,
        extensions: ws.extensions
      });
      showNotification('WebSocket连接成功', 'success');

      // 发送简单的连接确认
      const msg = {
        type: 'safari_plugin_connected',
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      };

      try {
        ws.send(JSON.stringify(msg));
        console.log('✓ 发送连接确认:', msg);
      } catch (error) {
        console.error('❌ 发送消息失败:', error);
      }
    });

    ws.addEventListener('message', function(event) {
      console.log('📨 收到消息:', event.data);

      try {
        const data = JSON.parse(event.data);
        console.log('✓ 解析后的数据:', data);

        if (data.command === 'setPlaybackRate') {
          console.log('🎵 收到倍速命令:', data.value);
          setPlaybackRate(data.value, 'websocket');
        }

        if (data.command === 'skipIntro') {
          console.log('⏭️ 收到跳过片头命令');
          skipIntro();
        }
      } catch (error) {
        console.log('📝 消息不是JSON格式:', event.data);
      }
    });

    ws.addEventListener('error', function(error) {
      console.error('❌ WebSocket错误详情:', {
        error: error,
        type: error.type,
        target: error.target,
        readyState: ws ? ws.readyState : 'undefined',
        url: ws ? ws.url : 'undefined',
        timestamp: Date.now()
      });
      console.error('❌ 完整错误对象:', error);
      showNotification('WebSocket连接错误', 'error');
    });

    ws.addEventListener('close', function(event) {
      console.log('🔌 WebSocket连接关闭详情:', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        type: event.type,
        timestamp: Date.now()
      });

      // 解释关闭代码
      let closeReason = '';
      switch(event.code) {
        case 1000: closeReason = '正常关闭'; break;
        case 1001: closeReason = '端点离开'; break;
        case 1002: closeReason = '协议错误'; break;
        case 1003: closeReason = '不支持的数据'; break;
        case 1006: closeReason = '连接异常关闭'; break;
        case 1011: closeReason = '服务器错误'; break;
        default: closeReason = `未知错误 (${event.code})`;
      }
      console.log('关闭原因:', closeReason);

      ws = null;

      // 不再自动重连
      console.log('❌ WebSocket连接已关闭，不再自动重连');
      showNotification('WebSocket连接已关闭', 'error');
    });

    // 添加状态检查
    console.log('WebSocket创建后状态检查:');
    console.log('- readyState:', ws.readyState);
    console.log('- url:', ws.url);
    console.log('- protocol:', ws.protocol);
    console.log('- extensions:', ws.extensions);

  } catch (error) {
    console.error('❌ 创建WebSocket失败:', {
      error: error,
      message: error.message,
      stack: error.stack,
      name: error.name,
      toString: error.toString()
    });
    console.error('❌ 完整错误:', error);
    showNotification('WebSocket创建失败: ' + error.message, 'error');
  }
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到popup消息:', request);
  
  if (request.action === 'setPlaybackRate') {
    const result = setPlaybackRate(request.rate, 'popup');
    sendResponse({success: result});
  }
  
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({success: true});
  }
  
  if (request.action === 'testWebSocket') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    console.log('WebSocket状态测试:', connected);
    sendResponse({connected: connected});
  }
});

// 测试函数
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试 ===');
  console.log('ws对象:', ws);
  
  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);
    
    if (ws.readyState === WebSocket.OPEN) {
      console.log('✓ WebSocket连接正常');
      
      // 发送测试消息
      try {
        const testMsg = JSON.stringify({command: 'test', timestamp: Date.now()});
        ws.send(testMsg);
        console.log('发送测试消息:', testMsg);
        return true;
      } catch (error) {
        console.error('发送测试消息失败:', error);
        return false;
      }
    } else {
      console.log('✗ WebSocket未连接');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    connectWebSocket();
    return false;
  }
};

window.testPlaybackRate = function(rate) {
  console.log(`测试设置倍速: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 手动重连WebSocket
window.reconnectWebSocket = function() {
  console.log('🔄 手动重连WebSocket...');
  if (ws) {
    ws.close();
    ws = null;
  }
  connectWebSocket();
};

// Safari特定的WebSocket连接检查
function checkSafariWebSocketSupport() {
  console.log('=== Safari WebSocket支持检查 ===');
  console.log('浏览器:', navigator.userAgent);
  console.log('WebSocket构造函数:', typeof WebSocket);
  console.log('WebSocket原型:', WebSocket.prototype);

  // 检查是否在Safari扩展环境中
  const isSafariExtension = typeof safari !== 'undefined' && safari.extension;
  console.log('Safari扩展环境:', isSafariExtension);

  // 检查安全上下文
  console.log('安全上下文 (isSecureContext):', window.isSecureContext);
  console.log('协议:', window.location.protocol);

  return typeof WebSocket !== 'undefined';
}

// 启动WebSocket连接（只尝试一次）
setTimeout(() => {
  console.log('🚀 启动WebSocket连接（仅尝试一次）...');

  // 先检查支持情况
  if (checkSafariWebSocketSupport()) {
    console.log('✓ WebSocket支持检查通过');
    connectWebSocket();
  } else {
    console.error('❌ WebSocket支持检查失败');
    showNotification('WebSocket不被支持', 'error');
  }
}, 2000);

console.log('=== Bilibili 控制插件初始化完成 ===');
