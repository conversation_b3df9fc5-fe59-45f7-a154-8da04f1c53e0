// 倍速播放
function setPlaybackRate(rate, source = 'unknown') {
  console.log(`=== setPlaybackRate 被调用 ===`);
  console.log(`参数: rate=${rate}, source=${source}`);
  console.log(`调用时间: ${new Date().toLocaleTimeString()}`);

  // 尝试多种方式查找视频元素
  let video = document.querySelector('video');
  console.log('第一次查找video:', video);

  // 如果没找到，尝试查找bilibili特定的视频容器
  if (!video) {
    console.log('尝试查找bilibili特定容器...');
    video = document.querySelector('.bpx-player-video-wrap video');
    console.log('.bpx-player-video-wrap video:', video);
  }
  if (!video) {
    video = document.querySelector('.bilibili-player-video video');
    console.log('.bilibili-player-video video:', video);
  }
  if (!video) {
    video = document.querySelector('[data-video] video');
    console.log('[data-video] video:', video);
  }
  if (!video) {
    // 查找所有video元素，选择第一个可见的
    console.log('查找所有video元素...');
    const videos = document.querySelectorAll('video');
    console.log(`找到 ${videos.length} 个video元素`);

    for (let i = 0; i < videos.length; i++) {
      const v = videos[i];
      console.log(`Video ${i + 1}: ${v.offsetWidth}x${v.offsetHeight}, 可见=${v.offsetWidth > 0 && v.offsetHeight > 0}`);
      if (v.offsetWidth > 0 && v.offsetHeight > 0) {
        video = v;
        console.log(`选择Video ${i + 1}作为目标`);
        break;
      }
    }
  }

  console.log('最终选择的视频元素:', video);

  if (video) {
    console.log('视频元素详细信息:', {
      src: video.src || video.currentSrc,
      duration: video.duration,
      currentTime: video.currentTime,
      playbackRate: video.playbackRate,
      paused: video.paused,
      readyState: video.readyState,
      width: video.offsetWidth,
      height: video.offsetHeight
    });

    try {
      // 确保倍速值在合理范围内
      const playbackRate = Math.max(0.25, Math.min(4, parseFloat(rate)));
      console.log(`准备设置倍速: ${video.playbackRate} -> ${playbackRate}`);

      // 记录设置前的状态
      const beforeRate = video.playbackRate;

      // 设置倍速
      video.playbackRate = playbackRate;
      console.log(`倍速设置命令已执行`);

      // 立即检查
      const immediateRate = video.playbackRate;
      console.log(`立即检查倍速: ${immediateRate}`);

      // 延迟验证设置是否成功
      setTimeout(() => {
        const finalRate = video.playbackRate;
        console.log(`延迟验证倍速: ${finalRate}`);

        if (Math.abs(finalRate - playbackRate) < 0.01) {
          console.log(`✓ 倍速设置成功: ${beforeRate} -> ${finalRate}`, 'success');
        } else {
          console.log(`✗ 倍速设置可能失败: 期望${playbackRate}, 实际${finalRate}`, 'warning');
        }
      }, 200);

      console.log(`视频倍速设置完成: ${playbackRate}x`);

      // 如果倍速值被调整了，显示警告
      if (Math.abs(playbackRate - parseFloat(rate)) > 0.01) {
        console.warn(`倍速值 ${rate} 超出范围，已调整为 ${playbackRate}`);
        showNotification(`倍速值已调整为 ${playbackRate}x (范围: 0.25x-4x)`, 'warning');
      }

      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      console.error('错误堆栈:', error.stack);
      showNotification('设置倍速失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    console.log('页面中的所有video元素:', document.querySelectorAll('video'));
    console.log('页面中的所有可能容器:');

    // 检查可能的容器
    const containers = [
      '.bpx-player',
      '.bpx-player-container',
      '.bpx-player-video-wrap',
      '.bilibili-player',
      '.bilibili-player-video',
      '#bilibili-player'
    ];

    containers.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      console.log(`${selector}: ${elements.length} 个元素`);
    });

    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 跳过片头（假设片头为前60秒）
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    try {
      const currentTime = video.currentTime;
      const skipToTime = 60; // 跳到60秒

      if (currentTime < skipToTime) {
        video.currentTime = skipToTime;
        console.log(`已跳过片头: ${currentTime.toFixed(1)}s -> ${skipToTime}s`);
        return true;
      } else {
        console.log(`当前时间 ${currentTime.toFixed(1)}s 已超过片头时间`);
        showNotification('当前已超过片头时间', 'info');
        return false;
      }
    } catch (error) {
      console.error('跳过片头失败:', error);
      showNotification('跳过片头失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 显示通知函数
function showNotification(message, type = 'info') {
  // 移除之前的通知
  const existingNotification = document.getElementById('bili-extension-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  const notification = document.createElement('div');
  notification.id = 'bili-extension-notification';

  // 根据类型设置颜色
  let backgroundColor = '#2196F3'; // 默认蓝色
  if (type === 'success') backgroundColor = '#4CAF50'; // 绿色
  else if (type === 'error') backgroundColor = '#f44336'; // 红色
  else if (type === 'warning') backgroundColor = '#ff9800'; // 橙色

  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${backgroundColor};
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    max-width: 300px;
  `;
  notification.textContent = message;

  document.body.appendChild(notification);

  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  }, 3000);
}

// 暂停视频
function pauseVideo() {
  const video = document.querySelector('video');
  if (video) {
    try {
      video.pause();
      console.log('视频已暂停');
      return true;
    } catch (error) {
      console.error('暂停视频失败:', error);
      showNotification('暂停视频失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 播放视频
function playVideo() {
  const video = document.querySelector('video');
  if (video) {
    try {
      video.play();
      console.log('视频已播放');
      return true;
    } catch (error) {
      console.error('播放视频失败:', error);
      showNotification('播放视频失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 设置音量
function setVolume(volume) {
  const video = document.querySelector('video');
  if (video) {
    try {
      // 确保音量值在0-1范围内
      const vol = Math.max(0, Math.min(1, parseFloat(volume)));
      video.volume = vol;
      console.log(`音量已设置为: ${Math.round(vol * 100)}%`);

      if (vol !== parseFloat(volume)) {
        console.warn(`音量值 ${volume} 超出范围，已调整为 ${vol}`);
        showNotification(`音量已调整为 ${Math.round(vol * 100)}% (范围: 0-100%)`, 'warning');
      }

      return true;
    } catch (error) {
      console.error('设置音量失败:', error);
      showNotification('设置音量失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 跳转到指定时间
function seekTo(time) {
  const video = document.querySelector('video');
  if (video) {
    try {
      const seekTime = Math.max(0, parseFloat(time));
      video.currentTime = seekTime;
      console.log(`已跳转到: ${seekTime}秒`);
      return true;
    } catch (error) {
      console.error('跳转失败:', error);
      showNotification('跳转失败', 'error');
      return false;
    }
  } else {
    console.warn('未找到视频元素');
    showNotification('未找到视频元素', 'warning');
    return false;
  }
}

// 监听来自 popup 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到popup消息:', request);

  if (request.action === 'setPlaybackRate') {
    console.log(`popup请求设置倍速为: ${request.rate}x`);
    if (setPlaybackRate(request.rate, 'popup')) {
      showNotification(`倍速已调整为 ${request.rate}x (popup)`, 'success');
      sendResponse({status: 'ok', message: `倍速设置为 ${request.rate}x`});
    } else {
      sendResponse({status: 'error', message: '设置倍速失败'});
    }
  }
  else if (request.action === 'skipIntro') {
    console.log('popup请求跳过片头');
    if (skipIntro()) {
      showNotification('已跳过片头 (popup)', 'info');
      sendResponse({status: 'ok', message: '已跳过片头'});
    } else {
      sendResponse({status: 'error', message: '跳过片头失败'});
    }
  }
  else {
    console.log('未识别的popup消息:', request);
    sendResponse({status: 'error', message: '未识别的操作'});
  }

  return true; // 保持消息通道开放以支持异步响应
});

// === WebSocket 连接监听 ===
let ws = null;
let reconnectInterval = null;

function connectWebSocket() {
  try {
    console.log('=== 开始WebSocket连接 ===');

    // 尝试多个WebSocket地址
    const wsUrls = [
      'wss://localhost:8001/scp',  // 主要WSS连接
      'ws://localhost:8001/scp',   // 备用WS连接
      'ws://127.0.0.1:8001/scp'    // IP地址连接
    ];

    console.log('当前时间:', new Date().toLocaleTimeString());
    console.log('页面URL:', window.location.href);
    console.log('尝试连接地址列表:', wsUrls);

    // 更新状态
    if (window.biliExtensionStatus) {
      window.biliExtensionStatus.websocketConnected = false;
    }

    // 尝试连接第一个地址
    connectToUrl(wsUrls, 0);

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    handleConnectionFailure();
  }
}

function connectToUrl(urls, index) {
  if (index >= urls.length) {
    console.error('所有WebSocket地址都连接失败');
    handleConnectionFailure();
    return;
  }

  const url = urls[index];
  console.log(`尝试连接WebSocket (${index + 1}/${urls.length}): ${url}`);

  try {
    ws = new WebSocket(url);
    console.log('WebSocket对象已创建:', ws);
    console.log('WebSocket readyState:', ws.readyState);

    ws.onopen = function(event) {
      console.log('✓ WebSocket 连接已建立!', event);
      console.log('连接地址:', ws.url);
      console.log('连接时间:', new Date().toLocaleTimeString());

      // 更新状态
      if (window.biliExtensionStatus) {
        window.biliExtensionStatus.websocketConnected = true;
        window.biliExtensionStatus.websocketUrl = ws.url;
      }

      // 更新加载指示器
      const loadingIndicator = document.getElementById('bili-extension-loaded');
      if (loadingIndicator) {
        const statusText = loadingIndicator.querySelector('div:last-child');
        if (statusText) {
          const protocol = ws.url.startsWith('wss:') ? 'WSS' : 'WS';
          statusText.innerHTML = `WebSocket: ✓ ${protocol}已连接 | 视频检测: 进行中...`;
        }
      }

      // 显示连接成功通知
      const protocol = ws.url.startsWith('wss:') ? 'WSS' : 'WS';
      showNotification(`${protocol} WebSocket连接成功`, 'success');

      // 清除重连定时器
      if (reconnectInterval) {
        clearInterval(reconnectInterval);
        reconnectInterval = null;
      }
    };

    ws.onerror = function(error) {
      console.error(`✗ WebSocket连接错误 (${url}):`, error);
      console.error('错误时间:', new Date().toLocaleTimeString());
      console.error('错误详情:', {
        readyState: ws ? ws.readyState : 'null',
        url: ws ? ws.url : 'null',
        type: error.type || 'unknown'
      });

      // 尝试下一个URL
      setTimeout(() => {
        connectToUrl(urls, index + 1);
      }, 1000);
    };

    ws.onclose = function(event) {
      console.log(`WebSocket连接已关闭 (${url})`, {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });

      // 更新状态
      if (window.biliExtensionStatus) {
        window.biliExtensionStatus.websocketConnected = false;
      }

      ws = null;

      // 如果不是正常关闭，尝试下一个URL或重连
      if (!event.wasClean) {
        if (index + 1 < urls.length) {
          console.log('尝试下一个WebSocket地址...');
          setTimeout(() => {
            connectToUrl(urls, index + 1);
          }, 1000);
        } else {
          console.log('所有地址都失败，启动重连机制...');
          handleConnectionFailure();
        }
      }
    };

    ws.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data);
        console.log('收到WebSocket消息:', data);

        // 处理新的消息格式 {"command":"setPlaybackRate","value":2}
        if (data.command === 'setPlaybackRate' && data.value !== undefined) {
          console.log(`WebSocket请求设置倍速为: ${data.value}x`);
          console.log('调用setPlaybackRate函数...');

          const result = setPlaybackRate(data.value, 'websocket');
          console.log(`setPlaybackRate函数返回: ${result}`);

          if (result) {
            showNotification(`倍速已调整为 ${data.value}x (WebSocket)`, 'success');
            console.log('WebSocket倍速设置成功');
          } else {
            showNotification(`倍速设置失败 (WebSocket)`, 'error');
            console.log('WebSocket倍速设置失败');
          }
        }
        else if (data.command === 'skipIntro') {
          console.log('执行跳过片头');
          if (skipIntro()) {
            showNotification('已跳过片头', 'info');
          }
        }
        else if (data.command === 'pause') {
          console.log('暂停视频');
          if (pauseVideo()) {
            showNotification('视频已暂停', 'info');
          }
        }
        else if (data.command === 'play') {
          console.log('播放视频');
          if (playVideo()) {
            showNotification('视频已播放', 'info');
          }
        }
        else if (data.command === 'setVolume' && data.value !== undefined) {
          console.log(`设置音量为: ${data.value}`);
          if (setVolume(data.value)) {
            showNotification(`音量已调整为 ${Math.round(data.value * 100)}%`, 'success');
          }
        }
        else if (data.command === 'seek' && data.value !== undefined) {
          console.log(`跳转到: ${data.value}秒`);
          if (seekTo(data.value)) {
            showNotification(`已跳转到 ${data.value}秒`, 'info');
          }
        }

        // 兼容旧的消息格式
        else if (data.action === 'setPlaybackRate') {
          console.log(`设置倍速为: ${data.rate}x (旧格式)`);
          setPlaybackRate(data.rate);
          showNotification(`倍速已调整为 ${data.rate}x`, 'success');
        }
        else if (data.action === 'skipIntro') {
          console.log('执行跳过片头 (旧格式)');
          skipIntro();
          showNotification('已跳过片头', 'info');
        }
        else {
          console.log('未识别的指令:', data);
        }
      } catch (e) {
        console.error('WebSocket 数据解析失败:', e);
      }
    };

    ws.onerror = function(error) {
      console.error('✗ WebSocket 连接错误:', error);
      console.error('错误时间:', new Date().toLocaleTimeString());
      console.error('错误详情:', {
        readyState: ws ? ws.readyState : 'null',
        url: ws ? ws.url : 'null',
        type: error.type || 'unknown',
        target: error.target || 'unknown'
      });

      // 更新状态
      if (window.biliExtensionStatus) {
        window.biliExtensionStatus.websocketConnected = false;
      }

      // 更新加载指示器
      const loadingIndicator = document.getElementById('bili-extension-loaded');
      if (loadingIndicator) {
        const statusText = loadingIndicator.querySelector('div:last-child');
        if (statusText) {
          statusText.innerHTML = 'WebSocket: ✗ 连接失败 | 视频检测: 进行中...';
        }
      }

      // 显示错误通知
      showNotification('WebSocket连接失败', 'error');
    };

    ws.onclose = function(event) {
      console.log('WebSocket 连接已关闭', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });
      ws = null;

      // 自动重连（每5秒尝试一次）
      if (!reconnectInterval) {
        console.log('启动自动重连机制...');
        reconnectInterval = setInterval(() => {
          console.log('尝试重新连接WebSocket...');
          connectWebSocket();
        }, 5000);
      }
    };

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    handleConnectionFailure();
  }
}

function handleConnectionFailure() {
  console.log('处理WebSocket连接失败...');

  // 更新状态
  if (window.biliExtensionStatus) {
    window.biliExtensionStatus.websocketConnected = false;
  }

  // 更新加载指示器
  const loadingIndicator = document.getElementById('bili-extension-loaded');
  if (loadingIndicator) {
    const statusText = loadingIndicator.querySelector('div:last-child');
    if (statusText) {
      statusText.innerHTML = 'WebSocket: ✗ 连接失败 | 视频检测: 进行中...';
    }
  }

  // 显示错误通知
  showNotification('WebSocket连接失败，将自动重试', 'error');

  // 启动重连机制
  if (!reconnectInterval) {
    console.log('启动自动重连机制...');
    reconnectInterval = setInterval(() => {
      console.log('尝试重新连接WebSocket...');
      connectWebSocket();
    }, 10000); // 10秒重试一次
  }
}

// 初始化WebSocket连接
console.log('=== Bilibili 控制插件已加载 ===');
console.log('当前页面URL:', window.location.href);
console.log('当前时间:', new Date().toLocaleTimeString());
console.log('User Agent:', navigator.userAgent);
console.log('Document ready state:', document.readyState);

// 创建一个更明显的加载指示器
function showLoadingIndicator() {
  const notification = document.createElement('div');
  notification.id = 'bili-extension-loaded';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 999999;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    border: 2px solid #fff;
    animation: slideIn 0.5s ease-out;
  `;

  // 添加CSS动画
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);

  notification.innerHTML = `
    <div>🎯 Bilibili控制插件已加载</div>
    <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
      WebSocket: 连接中... | 视频检测: 进行中...
    </div>
  `;

  // 确保body存在后再添加
  if (document.body) {
    document.body.appendChild(notification);
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      document.body.appendChild(notification);
    });
  }

  // 10秒后移除提示
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'slideOut 0.5s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 500);
    }
  }, 10000);

  return notification;
}

// 添加slideOut动画
const slideOutStyle = document.createElement('style');
slideOutStyle.textContent = `
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(slideOutStyle);

// 显示加载指示器
const loadingIndicator = showLoadingIndicator();

// 添加全局状态变量
window.biliExtensionStatus = {
  loaded: true,
  loadTime: new Date(),
  websocketConnected: false,
  videosFound: 0
};

// 添加测试按钮到页面
function addTestButtons() {
  const testPanel = document.createElement('div');
  testPanel.id = 'bili-extension-test-panel';
  testPanel.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 999998;
    font-size: 12px;
    min-width: 200px;
  `;

  testPanel.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 10px;">🔧 插件测试面板</div>
    <button onclick="window.testVideo()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">检测视频</button>
    <button onclick="window.testPlaybackRate(1.5)" style="margin: 2px; padding: 5px 8px; font-size: 11px;">测试1.5x</button>
    <button onclick="window.testPlaybackRate(2.0)" style="margin: 2px; padding: 5px 8px; font-size: 11px;">测试2.0x</button>
    <br>
    <button onclick="window.testWebSocket()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">检查WS</button>
    <button onclick="window.forceReconnectWebSocket()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">重连WS</button>
    <div style="margin-top: 8px; font-size: 10px; opacity: 0.7;">
      状态: <span id="extension-status-text">已加载</span>
    </div>
    <button onclick="document.getElementById('bili-extension-test-panel').remove()"
            style="margin-top: 5px; padding: 3px 6px; font-size: 10px; background: #666;">关闭</button>
  `;

  if (document.body) {
    document.body.appendChild(testPanel);
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      document.body.appendChild(testPanel);
    });
  }

  // 5分钟后自动移除测试面板
  setTimeout(() => {
    if (testPanel.parentNode) {
      testPanel.remove();
    }
  }, 300000);
}

// 延迟添加测试按钮
setTimeout(addTestButtons, 3000);

console.log('开始初始化WebSocket连接...');

// 延迟连接，确保页面完全加载
setTimeout(() => {
  console.log('延迟后开始连接WebSocket...');
  connectWebSocket();

  // 检测视频元素
  console.log('检测页面中的视频元素...');
  window.testVideo();
}, 2000);

// 监听页面变化，重新检测视频元素
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList') {
      const addedNodes = Array.from(mutation.addedNodes);
      const hasVideo = addedNodes.some(node =>
        node.nodeType === 1 && (
          node.tagName === 'VIDEO' ||
          node.querySelector && node.querySelector('video')
        )
      );

      if (hasVideo) {
        console.log('检测到新的视频元素，重新检测...');
        setTimeout(() => window.testVideo(), 500);
      }
    }
  });
});

// 开始观察DOM变化
observer.observe(document.body, {
  childList: true,
  subtree: true
});

// 添加测试函数
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试 ===');
  console.log('ws对象:', ws);

  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);

    if (ws.readyState === WebSocket.OPEN) {
      console.log('✓ WebSocket连接正常');

      // 发送测试消息
      try {
        const testMsg = JSON.stringify({command: 'test', timestamp: Date.now()});
        ws.send(testMsg);
        console.log('发送测试消息:', testMsg);
      } catch (error) {
        console.error('发送测试消息失败:', error);
      }

      return true;
    } else {
      console.log('✗ WebSocket未连接');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    return false;
  }
};

// 添加强制重连函数
window.forceReconnectWebSocket = function() {
  console.log('强制重新连接WebSocket...');

  // 关闭现有连接
  if (ws) {
    ws.close();
  }

  // 清除重连定时器
  if (reconnectInterval) {
    clearInterval(reconnectInterval);
    reconnectInterval = null;
  }

  // 立即重连
  setTimeout(connectWebSocket, 1000);
};

// 添加视频检测函数
window.testVideo = function() {
  console.log('=== 视频元素检测 ===');
  const videos = document.querySelectorAll('video');
  console.log(`找到 ${videos.length} 个video元素`);

  videos.forEach((video, index) => {
    console.log(`Video ${index + 1}:`, {
      src: video.src || video.currentSrc,
      duration: video.duration,
      currentTime: video.currentTime,
      playbackRate: video.playbackRate,
      paused: video.paused,
      width: video.offsetWidth,
      height: video.offsetHeight,
      visible: video.offsetWidth > 0 && video.offsetHeight > 0
    });
  });

  // 测试倍速设置
  if (videos.length > 0) {
    const video = videos[0];
    console.log('测试设置倍速为1.5x...');
    const oldRate = video.playbackRate;
    video.playbackRate = 1.5;
    setTimeout(() => {
      console.log(`倍速测试结果: ${oldRate} -> ${video.playbackRate}`);
    }, 100);
  }
};

// 添加手动倍速测试函数
window.testPlaybackRate = function(rate = 1.5) {
  console.log(`手动测试倍速设置: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
  if (reconnectInterval) {
    clearInterval(reconnectInterval);
  }
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close();
  }
});