// 倍速播放
function setPlaybackRate(rate) {
  const video = document.querySelector('video');
  if (video) {
    video.playbackRate = rate;
  }
}

// 跳过片头（假设片头为前60秒）
function skipIntro() {
  const video = document.querySelector('video');
  if (video && video.currentTime < 60) {
    video.currentTime = 60;
  }
}

// 监听来自 popup 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'setPlaybackRate') {
    setPlaybackRate(request.rate);
    sendResponse({status: 'ok'});
  }
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({status: 'ok'});
  }
});

// === MCP SSE 协议监听 ===
const sse = new EventSource('http://localhost:8000/sse');

sse.onmessage = function(event) {
  try {
    const data = JSON.parse(event.data);
    if (data.action === 'setPlaybackRate') {
      setPlaybackRate(data.rate);
    }
    if (data.action === 'skipIntro') {
      skipIntro();
    }
    // 可扩展更多指令
  } catch (e) {
    console.error('SSE 数据解析失败:', e);
  }
};

sse.onerror = function(err) {
  console.error('SSE 连接错误:', err);
}; 