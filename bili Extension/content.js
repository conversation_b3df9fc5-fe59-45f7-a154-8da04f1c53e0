// 简化的Safari插件 - Bilibili控制
console.log('=== Bilibili 控制插件已加载 (简化版) ===');

// WebSocket现在通过background script处理

// 设置视频倍速
function setPlaybackRate(rate, source = 'manual') {
  console.log(`设置倍速为 ${rate}x (来源: ${source})`);
  
  const video = document.querySelector('video');
  if (video) {
    try {
      video.playbackRate = rate;
      console.log(`✅ 倍速设置成功: ${rate}x`);
      showNotification(`倍速已设置为 ${rate}x`, 'success');
      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      return false;
    }
  } else {
    console.log('❌ 未找到视频元素');
    return false;
  }
}

// 跳过片头
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    video.currentTime = 60;
    console.log('已跳过片头');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  console.log(`通知 [${type}]: ${message}`);
  
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 通过background script连接WebSocket
function connectWebSocket() {
  console.log('=== 通过后台脚本连接WebSocket ===');

  // 请求background script连接WebSocket
  chrome.runtime.sendMessage({
    action: 'connect_websocket'
  }, (response) => {
    if (response && response.success) {
      console.log('✅ 后台WebSocket连接请求已发送');
    } else {
      console.error('❌ 后台WebSocket连接请求失败');
    }
  });
}

// 监听来自popup和background的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);

  if (request.action === 'setPlaybackRate') {
    const result = setPlaybackRate(request.rate, 'popup');
    sendResponse({success: result});
  }

  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({success: true});
  }

  if (request.action === 'testWebSocket') {
    // 检查background script的WebSocket状态
    chrome.runtime.sendMessage({action: 'get_websocket_status'}, (response) => {
      sendResponse({connected: response ? response.connected : false});
    });
    return true; // 保持异步响应
  }

  // 处理来自background script的WebSocket消息
  if (request.type === 'websocket_connected') {
    console.log('✅ 收到WebSocket连接成功通知');
    showNotification('WebSocket连接成功', 'success');
  }

  if (request.type === 'websocket_command') {
    console.log('🎯 收到WebSocket命令:', request.command, request.value);

    if (request.command === 'setPlaybackRate') {
      console.log('🎵 设置倍速:', request.value);
      setPlaybackRate(request.value, 'websocket');
    }

    if (request.command === 'skipIntro') {
      console.log('⏭️ 跳过片头');
      skipIntro();
    }
  }
});

// 测试函数
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试（通过后台脚本）===');

  chrome.runtime.sendMessage({action: 'get_websocket_status'}, (response) => {
    if (response && response.connected) {
      console.log('✓ 后台WebSocket连接正常');

      // 发送测试消息
      const testMsg = {command: 'test', timestamp: Date.now()};
      chrome.runtime.sendMessage({
        action: 'send_websocket_message',
        message: testMsg
      }, (response) => {
        if (response && response.success) {
          console.log('✓ 测试消息发送成功');
        } else {
          console.error('✗ 测试消息发送失败:', response ? response.error : '未知错误');
        }
      });
    } else {
      console.log('✗ 后台WebSocket未连接');
      connectWebSocket();
    }
  });
};

window.testPlaybackRate = function(rate) {
  console.log(`测试设置倍速: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 手动重连WebSocket
window.reconnectWebSocket = function() {
  console.log('🔄 手动重连WebSocket...');
  if (ws) {
    ws.close();
    ws = null;
  }
  connectWebSocket();
};

// Safari特定的WebSocket连接检查
function checkSafariWebSocketSupport() {
  console.log('=== Safari WebSocket支持检查 ===');
  console.log('浏览器:', navigator.userAgent);
  console.log('WebSocket构造函数:', typeof WebSocket);
  console.log('WebSocket原型:', WebSocket.prototype);

  // 检查是否在Safari扩展环境中
  const isSafariExtension = typeof safari !== 'undefined' && safari.extension;
  console.log('Safari扩展环境:', isSafariExtension);

  // 检查安全上下文
  console.log('安全上下文 (isSecureContext):', window.isSecureContext);
  console.log('协议:', window.location.protocol);

  return typeof WebSocket !== 'undefined';
}

// 启动WebSocket连接（只尝试一次）
setTimeout(() => {
  console.log('🚀 启动WebSocket连接（仅尝试一次）...');

  // 先检查支持情况
  if (checkSafariWebSocketSupport()) {
    console.log('✓ WebSocket支持检查通过');
    connectWebSocket();
  } else {
    console.error('❌ WebSocket支持检查失败');
    showNotification('WebSocket不被支持', 'error');
  }
}, 2000);

console.log('=== Bilibili 控制插件初始化完成 ===');
