// 简化的Safari插件 - Bilibili控制
console.log('=== Bilibili 控制插件已加载 (简化版) ===');

// WebSocket连接变量
let ws = null;

// 设置视频倍速
function setPlaybackRate(rate, source = 'manual') {
  console.log(`设置倍速为 ${rate}x (来源: ${source})`);
  
  const video = document.querySelector('video');
  if (video) {
    try {
      video.playbackRate = rate;
      console.log(`✅ 倍速设置成功: ${rate}x`);
      showNotification(`倍速已设置为 ${rate}x`, 'success');
      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      return false;
    }
  } else {
    console.log('❌ 未找到视频元素');
    return false;
  }
}

// 跳过片头
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    video.currentTime = 60;
    console.log('已跳过片头');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  console.log(`通知 [${type}]: ${message}`);
  
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 极简WebSocket连接测试
function connectWebSocket() {
  console.log('=== 极简WebSocket连接测试 ===');

  const url = 'ws://localhost:8002/scp';
  console.log('连接地址:', url);
  console.log('WebSocket构造函数存在:', typeof WebSocket);

  try {
    console.log('创建WebSocket...');
    ws = new WebSocket(url);

    console.log('WebSocket对象:', ws);
    console.log('初始状态:', ws.readyState);

    // 最简单的事件处理
    ws.onopen = function() {
      console.log('✅ 连接成功!');
    };

    ws.onerror = function(e) {
      console.error('❌ 连接错误:', e);
      console.error('错误类型:', e.type);
      console.error('错误目标:', e.target);
      console.error('WebSocket状态:', ws.readyState);
    };

    ws.onclose = function(e) {
      console.log('🔌 连接关闭:', e.code, e.reason);
    };

    ws.onmessage = function(e) {
      console.log('📨 收到消息:', e.data);
    };

  } catch (error) {
    console.error('❌ 创建WebSocket失败:', error);
    console.error('错误消息:', error.message);
    console.error('错误名称:', error.name);
  }
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到popup消息:', request);
  
  if (request.action === 'setPlaybackRate') {
    const result = setPlaybackRate(request.rate, 'popup');
    sendResponse({success: result});
  }
  
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({success: true});
  }
  
  if (request.action === 'testWebSocket') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    console.log('WebSocket状态测试:', connected);
    sendResponse({connected: connected});
  }
});

// 测试函数
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试 ===');
  console.log('ws对象:', ws);
  
  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);
    
    if (ws.readyState === WebSocket.OPEN) {
      console.log('✓ WebSocket连接正常');
      
      // 发送测试消息
      try {
        const testMsg = JSON.stringify({command: 'test', timestamp: Date.now()});
        ws.send(testMsg);
        console.log('发送测试消息:', testMsg);
        return true;
      } catch (error) {
        console.error('发送测试消息失败:', error);
        return false;
      }
    } else {
      console.log('✗ WebSocket未连接');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    connectWebSocket();
    return false;
  }
};

window.testPlaybackRate = function(rate) {
  console.log(`测试设置倍速: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 手动重连WebSocket
window.reconnectWebSocket = function() {
  console.log('🔄 手动重连WebSocket...');
  if (ws) {
    ws.close();
    ws = null;
  }
  connectWebSocket();
};

// Safari特定的WebSocket连接检查
function checkSafariWebSocketSupport() {
  console.log('=== Safari WebSocket支持检查 ===');
  console.log('浏览器:', navigator.userAgent);
  console.log('WebSocket构造函数:', typeof WebSocket);
  console.log('WebSocket原型:', WebSocket.prototype);

  // 检查是否在Safari扩展环境中
  const isSafariExtension = typeof safari !== 'undefined' && safari.extension;
  console.log('Safari扩展环境:', isSafariExtension);

  // 检查安全上下文
  console.log('安全上下文 (isSecureContext):', window.isSecureContext);
  console.log('协议:', window.location.protocol);

  return typeof WebSocket !== 'undefined';
}

// 启动WebSocket连接（只尝试一次）
setTimeout(() => {
  console.log('🚀 启动WebSocket连接（仅尝试一次）...');

  // 先检查支持情况
  if (checkSafariWebSocketSupport()) {
    console.log('✓ WebSocket支持检查通过');
    connectWebSocket();
  } else {
    console.error('❌ WebSocket支持检查失败');
    showNotification('WebSocket不被支持', 'error');
  }
}, 2000);

console.log('=== Bilibili 控制插件初始化完成 ===');
