// 简化的Safari插件 - Bilibili控制
console.log('=== Bilibili 控制插件已加载 (简化版) ===');

// WebSocket连接变量
let ws = null;

// 设置视频倍速
function setPlaybackRate(rate, source = 'manual') {
  console.log(`设置倍速为 ${rate}x (来源: ${source})`);
  
  const video = document.querySelector('video');
  if (video) {
    try {
      video.playbackRate = rate;
      console.log(`✅ 倍速设置成功: ${rate}x`);
      showNotification(`倍速已设置为 ${rate}x`, 'success');
      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      return false;
    }
  } else {
    console.log('❌ 未找到视频元素');
    return false;
  }
}

// 跳过片头
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    video.currentTime = 60;
    console.log('已跳过片头');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  console.log(`通知 [${type}]: ${message}`);
  
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 简化的WebSocket连接函数
function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已连接');
    return;
  }

  console.log('=== 开始简化WebSocket连接 ===');
  const url = 'ws://localhost:8002/scp';
  console.log('连接地址:', url);

  try {
    ws = new WebSocket(url);
    console.log('WebSocket对象已创建');

    ws.onopen = function(event) {
      console.log('✅ WebSocket连接成功!');
      showNotification('WebSocket连接成功', 'success');
      
      // 发送简单的连接确认
      const msg = {
        type: 'safari_plugin_connected',
        timestamp: Date.now(),
        url: window.location.href
      };
      
      try {
        ws.send(JSON.stringify(msg));
        console.log('发送连接确认:', msg);
      } catch (error) {
        console.error('发送消息失败:', error);
      }
    };

    ws.onmessage = function(event) {
      console.log('📨 收到消息:', event.data);
      
      try {
        const data = JSON.parse(event.data);
        console.log('解析后的数据:', data);
        
        if (data.command === 'setPlaybackRate') {
          console.log('🎵 收到倍速命令:', data.value);
          setPlaybackRate(data.value, 'websocket');
        }
        
        if (data.command === 'skipIntro') {
          console.log('⏭️ 收到跳过片头命令');
          skipIntro();
        }
      } catch (error) {
        console.log('消息不是JSON格式:', event.data);
      }
    };

    ws.onerror = function(error) {
      console.error('❌ WebSocket错误:', error);
      showNotification('WebSocket连接错误', 'error');
    };

    ws.onclose = function(event) {
      console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
      ws = null;
      
      // 5秒后重试
      setTimeout(() => {
        console.log('5秒后重试连接...');
        connectWebSocket();
      }, 5000);
    };

  } catch (error) {
    console.error('创建WebSocket失败:', error);
    showNotification('WebSocket创建失败', 'error');
  }
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到popup消息:', request);
  
  if (request.action === 'setPlaybackRate') {
    const result = setPlaybackRate(request.rate, 'popup');
    sendResponse({success: result});
  }
  
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({success: true});
  }
  
  if (request.action === 'testWebSocket') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    console.log('WebSocket状态测试:', connected);
    sendResponse({connected: connected});
  }
});

// 测试函数
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试 ===');
  console.log('ws对象:', ws);
  
  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);
    
    if (ws.readyState === WebSocket.OPEN) {
      console.log('✓ WebSocket连接正常');
      
      // 发送测试消息
      try {
        const testMsg = JSON.stringify({command: 'test', timestamp: Date.now()});
        ws.send(testMsg);
        console.log('发送测试消息:', testMsg);
        return true;
      } catch (error) {
        console.error('发送测试消息失败:', error);
        return false;
      }
    } else {
      console.log('✗ WebSocket未连接');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    connectWebSocket();
    return false;
  }
};

window.testPlaybackRate = function(rate) {
  console.log(`测试设置倍速: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 启动WebSocket连接
setTimeout(() => {
  console.log('启动WebSocket连接...');
  connectWebSocket();
}, 2000);

console.log('=== Bilibili 控制插件初始化完成 ===');
