// 倍速播放
function setPlaybackRate(rate) {
  const video = document.querySelector('video');
  if (video) {
    video.playbackRate = rate;
  }
}

// 跳过片头（假设片头为前60秒）
function skipIntro() {
  const video = document.querySelector('video');
  if (video && video.currentTime < 60) {
    video.currentTime = 60;
  }
}

// 监听来自 popup 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'setPlaybackRate') {
    setPlaybackRate(request.rate);
    sendResponse({status: 'ok'});
  }
  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({status: 'ok'});
  }
});

// === WebSocket 连接监听 ===
let ws = null;
let reconnectInterval = null;

function connectWebSocket() {
  try {
    ws = new WebSocket('ws://localhost:8001/scp');

    ws.onopen = function(event) {
      console.log('WebSocket 连接已建立');
      // 清除重连定时器
      if (reconnectInterval) {
        clearInterval(reconnectInterval);
        reconnectInterval = null;
      }
    };

    ws.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data);
        console.log('收到WebSocket消息:', data);

        if (data.action === 'setPlaybackRate') {
          setPlaybackRate(data.rate);
        }
        if (data.action === 'skipIntro') {
          skipIntro();
        }
        // 可扩展更多指令
      } catch (e) {
        console.error('WebSocket 数据解析失败:', e);
      }
    };

    ws.onerror = function(error) {
      console.error('WebSocket 连接错误:', error);
    };

    ws.onclose = function(event) {
      console.log('WebSocket 连接已关闭, 代码:', event.code, '原因:', event.reason);
      ws = null;

      // 自动重连（每5秒尝试一次）
      if (!reconnectInterval) {
        reconnectInterval = setInterval(() => {
          console.log('尝试重新连接WebSocket...');
          connectWebSocket();
        }, 5000);
      }
    };

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);

    // 连接失败时也启动重连机制
    if (!reconnectInterval) {
      reconnectInterval = setInterval(() => {
        console.log('尝试重新连接WebSocket...');
        connectWebSocket();
      }, 5000);
    }
  }
}

// 初始化WebSocket连接
connectWebSocket();

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
  if (reconnectInterval) {
    clearInterval(reconnectInterval);
  }
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close();
  }
});