// 简化的Safari插件 - Bilibili控制
console.log('=== Bilibili 控制插件已加载 (简化版) ===');

// WebSocket现在通过background script处理

// 设置视频倍速
function setPlaybackRate(rate, source = 'manual') {
  console.log(`设置倍速为 ${rate}x (来源: ${source})`);
  
  const video = document.querySelector('video');
  if (video) {
    try {
      video.playbackRate = rate;
      console.log(`✅ 倍速设置成功: ${rate}x`);
      showNotification(`倍速已设置为 ${rate}x`, 'success');
      return true;
    } catch (error) {
      console.error('设置倍速失败:', error);
      return false;
    }
  } else {
    console.log('❌ 未找到视频元素');
    return false;
  }
}

// 跳过片头
function skipIntro() {
  const video = document.querySelector('video');
  if (video) {
    video.currentTime = 60;
    console.log('已跳过片头');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  console.log(`通知 [${type}]: ${message}`);
  
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 直接在content script中连接WebSocket（使用测试页面的成功模式）
let ws = null;

function connectWebSocket() {
  // 在HTTPS页面中，浏览器会阻止连接到不安全的WebSocket
  const url = 'ws://localhost:8002/scp';

  console.log('🔒 安全上下文检测:');
  console.log('- 当前协议:', window.location.protocol);
  console.log('- 是否安全上下文:', window.isSecureContext);
  console.log('- 尝试连接:', url);
  console.log('⚠️ 注意：HTTPS页面连接WS://可能被浏览器阻止');

  if (!url) {
    console.error('WebSocket地址为空');
    return;
  }

  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log('WebSocket已经连接');
    return;
  }

  console.log('=== 直接WebSocket连接（测试页面模式）===');
  console.log('连接地址:', url);
  console.log('WebSocket构造函数类型:', typeof WebSocket);

  const connectStartTime = Date.now();

  try {
    ws = new WebSocket(url);
    console.log('✓ WebSocket对象已创建');
    console.log('初始状态:', ws.readyState);

    ws.onopen = function(event) {
      const connectTime = Date.now() - connectStartTime;
      console.log(`✅ WebSocket连接成功! (耗时: ${connectTime}ms)`);
      console.log('连接事件:', event);
      console.log('WebSocket状态:', ws.readyState);
      console.log('WebSocket URL:', ws.url);
      console.log('WebSocket协议:', ws.protocol);
      console.log('WebSocket扩展:', ws.extensions);

      showNotification('WebSocket连接成功', 'success');

      // 发送连接确认消息（模仿测试页面）
      const welcomeMsg = {
        type: 'safari_content_script_connected',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        source: 'safari_extension_content',
        connectTime: connectTime
      };

      try {
        ws.send(JSON.stringify(welcomeMsg));
        console.log('✓ 发送连接确认消息:', welcomeMsg);
      } catch (error) {
        console.error('❌ 发送连接确认消息失败:', error);
      }
    };

    ws.onmessage = function(event) {
      console.log('📨 收到WebSocket消息');
      console.log('原始数据:', event.data);

      try {
        const data = JSON.parse(event.data);
        console.log('✅ JSON解析成功:', data);

        // 计算延迟
        if (data.timestamp) {
          const latency = Date.now() - data.timestamp;
          console.log(`⏱️ 消息延迟: ${latency}ms`);
        }

        // 处理命令
        if (data.command === 'setPlaybackRate') {
          console.log('🎵 收到倍速命令:', data.value);
          setPlaybackRate(data.value, 'websocket');
        }

        if (data.command === 'skipIntro') {
          console.log('⏭️ 收到跳过片头命令');
          skipIntro();
        }

      } catch (error) {
        console.log('❌ JSON解析失败:', error.message);
        console.log('原始数据:', event.data);
      }
    };

    ws.onerror = function(error) {
      console.error('❌ WebSocket连接错误详情:');
      console.error('- 错误事件:', error);
      console.error('- 错误类型:', error.type);
      console.error('- 错误目标:', error.target);
      console.error('- WebSocket状态:', ws ? ws.readyState : 'undefined');
      console.error('- WebSocket URL:', ws ? ws.url : 'undefined');
      console.error('- 时间戳:', new Date().toISOString());
      console.error('- 页面协议:', window.location.protocol);
      console.error('- 安全上下文:', window.isSecureContext);

      // 检查是否是混合内容错误
      if (window.location.protocol === 'https:' && ws.url.startsWith('ws://')) {
        console.error('🚨 混合内容错误：HTTPS页面不能连接到不安全的WebSocket (ws://)');
        console.error('💡 解决方案：需要使用安全的WebSocket (wss://) 或配置服务器支持SSL');
        showNotification('混合内容错误：需要WSS连接', 'error');
      } else {
        showNotification('WebSocket连接错误', 'error');
      }
    };

    ws.onclose = function(event) {
      console.log('🔌 WebSocket连接关闭详情:');
      console.log('- 关闭代码:', event.code);
      console.log('- 关闭原因:', event.reason);
      console.log('- 是否干净关闭:', event.wasClean);
      console.log('- 事件类型:', event.type);
      console.log('- 时间戳:', new Date().toISOString());

      // 解释关闭代码
      let closeReason = '';
      switch(event.code) {
        case 1000: closeReason = '正常关闭'; break;
        case 1001: closeReason = '端点离开'; break;
        case 1002: closeReason = '协议错误'; break;
        case 1003: closeReason = '不支持的数据'; break;
        case 1006: closeReason = '连接异常关闭'; break;
        case 1011: closeReason = '服务器错误'; break;
        default: closeReason = `未知错误 (${event.code})`;
      }
      console.log('关闭原因解释:', closeReason);

      ws = null;
      showNotification('WebSocket连接已关闭', 'error');
    };

  } catch (error) {
    console.error('❌ 创建WebSocket失败:', error);
    console.error('错误消息:', error.message);
    console.error('错误名称:', error.name);
    showNotification('WebSocket创建失败: ' + error.message, 'error');
  }
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到popup消息:', request);

  if (request.action === 'setPlaybackRate') {
    const result = setPlaybackRate(request.rate, 'popup');
    sendResponse({success: result});
  }

  if (request.action === 'skipIntro') {
    skipIntro();
    sendResponse({success: true});
  }

  if (request.action === 'testWebSocket') {
    const connected = ws && ws.readyState === WebSocket.OPEN;
    console.log('WebSocket状态测试:', connected);
    sendResponse({connected: connected});
  }
});

// 测试函数（直接WebSocket模式）
window.testWebSocket = function() {
  console.log('=== WebSocket连接测试（直接模式）===');
  console.log('ws对象:', ws);

  if (ws) {
    const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    console.log('WebSocket状态:', states[ws.readyState] || ws.readyState);
    console.log('WebSocket URL:', ws.url);

    if (ws.readyState === WebSocket.OPEN) {
      console.log('✓ WebSocket连接正常');

      // 发送测试消息
      try {
        const testMsg = {
          type: 'test_message',
          timestamp: Date.now(),
          message: 'Hello from Safari extension!',
          random: Math.random().toString(36).substr(2, 9)
        };
        ws.send(JSON.stringify(testMsg));
        console.log('✓ 发送测试消息:', testMsg);
        return true;
      } catch (error) {
        console.error('✗ 发送测试消息失败:', error);
        return false;
      }
    } else {
      console.log('✗ WebSocket未连接');
      return false;
    }
  } else {
    console.log('✗ WebSocket对象不存在');
    connectWebSocket();
    return false;
  }
};

window.testPlaybackRate = function(rate) {
  console.log(`测试设置倍速: ${rate}x`);
  return setPlaybackRate(rate, 'manual_test');
};

// 手动重连WebSocket
window.reconnectWebSocket = function() {
  console.log('🔄 手动重连WebSocket...');
  if (ws) {
    ws.close();
    ws = null;
  }
  connectWebSocket();
};

// Safari特定的WebSocket连接检查
function checkSafariWebSocketSupport() {
  console.log('=== Safari WebSocket支持检查 ===');
  console.log('浏览器:', navigator.userAgent);
  console.log('WebSocket构造函数:', typeof WebSocket);
  console.log('WebSocket原型:', WebSocket.prototype);

  // 检查是否在Safari扩展环境中
  const isSafariExtension = typeof safari !== 'undefined' && safari.extension;
  console.log('Safari扩展环境:', isSafariExtension);

  // 检查安全上下文
  console.log('安全上下文 (isSecureContext):', window.isSecureContext);
  console.log('协议:', window.location.protocol);

  return typeof WebSocket !== 'undefined';
}

// 启动WebSocket连接（只尝试一次）
setTimeout(() => {
  console.log('🚀 启动WebSocket连接（仅尝试一次）...');

  // 先检查支持情况
  if (checkSafariWebSocketSupport()) {
    console.log('✓ WebSocket支持检查通过');
    connectWebSocket();
  } else {
    console.error('❌ WebSocket支持检查失败');
    showNotification('WebSocket不被支持', 'error');
  }
}, 2000);

console.log('=== Bilibili 控制插件初始化完成 ===');
