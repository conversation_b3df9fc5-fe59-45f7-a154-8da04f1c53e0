#!/usr/bin/env python3
"""
简单的WebSocket测试服务器
"""

import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def handle_client(websocket):
    """处理客户端连接"""
    client_address = websocket.remote_address
    path = websocket.path
    logger.info(f"新客户端连接: {client_address} 路径: {path}")
    
    try:
        # 发送欢迎消息
        welcome_msg = {
            "action": "welcome",
            "message": "WebSocket连接成功建立"
        }
        await websocket.send(json.dumps(welcome_msg))
        
        # 监听客户端消息
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"收到来自 {client_address} 的消息: {data}")
                
                # 回复确认消息
                response = {
                    "action": "ack",
                    "received": data
                }
                await websocket.send(json.dumps(response))
                
            except json.JSONDecodeError:
                logger.warning(f"收到无效JSON消息: {message}")
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"客户端 {client_address} 断开连接")
    except Exception as e:
        logger.error(f"处理客户端 {client_address} 时发生错误: {e}")

async def main():
    """主函数"""
    logger.info("启动WebSocket服务器在 ws://localhost:8001/scp")
    
    # 启动WebSocket服务器
    server = await websockets.serve(handle_client, "localhost", 8001)
    
    logger.info("WebSocket服务器已启动，等待连接...")
    
    try:
        await server.wait_closed()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        server.close()
        await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器已停止")
