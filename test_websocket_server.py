#!/usr/bin/env python3
"""
简单的WebSocket测试服务器
用于测试Safari插件的WebSocket连接
"""

import asyncio
import websockets
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def handle_client(websocket, path):
    """处理客户端连接"""
    client_address = websocket.remote_address
    logger.info(f"新客户端连接: {client_address} 路径: {path}")
    
    try:
        # 发送欢迎消息
        welcome_msg = {
            "action": "welcome",
            "message": "WebSocket连接成功建立",
            "timestamp": asyncio.get_event_loop().time()
        }
        await websocket.send(json.dumps(welcome_msg))
        
        # 监听客户端消息
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"收到来自 {client_address} 的消息: {data}")
                
                # 回复确认消息
                response = {
                    "action": "ack",
                    "received": data,
                    "timestamp": asyncio.get_event_loop().time()
                }
                await websocket.send(json.dumps(response))
                
            except json.JSONDecodeError:
                logger.warning(f"收到无效JSON消息: {message}")
                error_response = {
                    "action": "error",
                    "message": "无效的JSON格式",
                    "timestamp": asyncio.get_event_loop().time()
                }
                await websocket.send(json.dumps(error_response))
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"客户端 {client_address} 断开连接")
    except Exception as e:
        logger.error(f"处理客户端 {client_address} 时发生错误: {e}")

async def send_test_commands():
    """定期发送测试命令"""
    await asyncio.sleep(5)  # 等待5秒让客户端连接
    
    while True:
        try:
            # 获取所有连接的客户端
            if hasattr(send_test_commands, 'clients'):
                for websocket in send_test_commands.clients.copy():
                    try:
                        # 发送倍速播放命令
                        test_cmd = {
                            "action": "setPlaybackRate",
                            "rate": 1.5,
                            "timestamp": asyncio.get_event_loop().time()
                        }
                        await websocket.send(json.dumps(test_cmd))
                        logger.info(f"发送测试命令: {test_cmd}")
                        
                        await asyncio.sleep(10)  # 等待10秒
                        
                        # 发送跳过片头命令
                        skip_cmd = {
                            "action": "skipIntro",
                            "timestamp": asyncio.get_event_loop().time()
                        }
                        await websocket.send(json.dumps(skip_cmd))
                        logger.info(f"发送跳过片头命令: {skip_cmd}")
                        
                    except websockets.exceptions.ConnectionClosed:
                        send_test_commands.clients.discard(websocket)
                    except Exception as e:
                        logger.error(f"发送测试命令时出错: {e}")
                        
        except Exception as e:
            logger.error(f"发送测试命令循环出错: {e}")
            
        await asyncio.sleep(30)  # 每30秒发送一次测试命令

async def main():
    """主函数"""
    # 存储客户端连接
    send_test_commands.clients = set()
    
    async def handle_client_with_tracking(websocket):
        send_test_commands.clients.add(websocket)
        try:
            await handle_client(websocket, websocket.path)
        finally:
            send_test_commands.clients.discard(websocket)
    
    # 启动WebSocket服务器
    logger.info("启动WebSocket服务器在 ws://localhost:8001/scp")
    server = await websockets.serve(
        handle_client_with_tracking,
        "localhost",
        8001
    )
    
    # 启动测试命令发送任务
    test_task = asyncio.create_task(send_test_commands())
    
    logger.info("WebSocket服务器已启动，等待连接...")
    
    try:
        await server.wait_closed()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        test_task.cancel()
        server.close()
        await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器已停止")
