#!/usr/bin/env python3
"""
测试WebSocket命令发送脚本
用于测试Safari插件的倍速调整功能
"""

import asyncio
import websockets
import json
import time

async def send_test_commands():
    """发送测试命令到Safari插件"""
    try:
        # 连接到WebSocket服务器
        uri = "ws://localhost:8001/scp"
        print(f"连接到 {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("WebSocket连接成功建立")
            
            # 等待一下确保插件已连接
            await asyncio.sleep(2)
            
            # 测试命令列表
            test_commands = [
                {"command": "setPlaybackRate", "value": 1.5},
                {"command": "setPlaybackRate", "value": 2.0},
                {"command": "setPlaybackRate", "value": 0.5},
                {"command": "setPlaybackRate", "value": 1.0},
                {"command": "pause"},
                {"command": "play"},
                {"command": "setVolume", "value": 0.5},
                {"command": "setVolume", "value": 1.0},
                {"command": "skipIntro"},
                {"command": "seek", "value": 30}
            ]
            
            for i, cmd in enumerate(test_commands, 1):
                print(f"\n[{i}/{len(test_commands)}] 发送命令: {json.dumps(cmd, ensure_ascii=False)}")
                
                # 发送命令
                await websocket.send(json.dumps(cmd))
                
                # 等待一下再发送下一个命令
                await asyncio.sleep(3)
                
                # 检查是否有回复
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    print(f"收到回复: {response}")
                except asyncio.TimeoutError:
                    print("没有收到回复")
            
            print("\n所有测试命令发送完成！")
            
    except websockets.exceptions.ConnectionRefused:
        print("错误: 无法连接到WebSocket服务器")
        print("请确保服务器在 ws://localhost:8001/scp 运行")
    except Exception as e:
        print(f"发生错误: {e}")

async def interactive_mode():
    """交互模式 - 手动发送命令"""
    try:
        uri = "ws://localhost:8001/scp"
        print(f"连接到 {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("WebSocket连接成功建立")
            print("\n可用命令:")
            print("1. setPlaybackRate <倍速值> - 设置播放倍速 (例如: 1.5, 2.0)")
            print("2. pause - 暂停视频")
            print("3. play - 播放视频")
            print("4. setVolume <音量值> - 设置音量 (0.0-1.0)")
            print("5. skipIntro - 跳过片头")
            print("6. seek <时间> - 跳转到指定时间(秒)")
            print("7. quit - 退出")
            print("\n输入命令 (格式: command value):")
            
            while True:
                try:
                    user_input = input("> ").strip()
                    if not user_input:
                        continue
                        
                    if user_input.lower() == 'quit':
                        break
                    
                    parts = user_input.split()
                    command = parts[0]
                    
                    if command == "setPlaybackRate" and len(parts) > 1:
                        try:
                            value = float(parts[1])
                            cmd = {"command": "setPlaybackRate", "value": value}
                        except ValueError:
                            print("错误: 倍速值必须是数字")
                            continue
                    elif command == "setVolume" and len(parts) > 1:
                        try:
                            value = float(parts[1])
                            cmd = {"command": "setVolume", "value": value}
                        except ValueError:
                            print("错误: 音量值必须是数字")
                            continue
                    elif command == "seek" and len(parts) > 1:
                        try:
                            value = float(parts[1])
                            cmd = {"command": "seek", "value": value}
                        except ValueError:
                            print("错误: 时间值必须是数字")
                            continue
                    elif command in ["pause", "play", "skipIntro"]:
                        cmd = {"command": command}
                    else:
                        print("错误: 未知命令或缺少参数")
                        continue
                    
                    print(f"发送: {json.dumps(cmd, ensure_ascii=False)}")
                    await websocket.send(json.dumps(cmd))
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"发送命令时出错: {e}")
            
            print("退出交互模式")
            
    except Exception as e:
        print(f"连接错误: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        print("启动交互模式...")
        asyncio.run(interactive_mode())
    else:
        print("启动自动测试模式...")
        print("使用 'python3 test_commands.py interactive' 启动交互模式")
        asyncio.run(send_test_commands())
