// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		C3FF09BC2E141EEE003EBFE3 /* bili Extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = C3FF09BB2E141EEE003EBFE3 /* bili Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C3FF09A62E141EEE003EBFE3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C3FF09812E141EED003EBFE3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C3FF09882E141EED003EBFE3;
			remoteInfo = bili;
		};
		C3FF09B02E141EEE003EBFE3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C3FF09812E141EED003EBFE3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C3FF09882E141EED003EBFE3;
			remoteInfo = bili;
		};
		C3FF09BD2E141EEE003EBFE3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C3FF09812E141EED003EBFE3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C3FF09BA2E141EEE003EBFE3;
			remoteInfo = "bili Extension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C3FF09DB2E141EEE003EBFE3 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				C3FF09BC2E141EEE003EBFE3 /* bili Extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		C3FF09892E141EED003EBFE3 /* bili.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = bili.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C3FF09A52E141EEE003EBFE3 /* biliTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = biliTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		C3FF09AF2E141EEE003EBFE3 /* biliUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = biliUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		C3FF09BB2E141EEE003EBFE3 /* bili Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "bili Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		C3FF09D52E141EEE003EBFE3 /* Exceptions for "bili Extension" folder in "bili Extension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = C3FF09BA2E141EEE003EBFE3 /* bili Extension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C3FF098B2E141EED003EBFE3 /* bili */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = bili;
			sourceTree = "<group>";
		};
		C3FF09A82E141EEE003EBFE3 /* biliTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = biliTests;
			sourceTree = "<group>";
		};
		C3FF09B22E141EEE003EBFE3 /* biliUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = biliUITests;
			sourceTree = "<group>";
		};
		C3FF09BF2E141EEE003EBFE3 /* bili Extension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				C3FF09D52E141EEE003EBFE3 /* Exceptions for "bili Extension" folder in "bili Extension" target */,
			);
			explicitFolders = (
				Resources/_locales,
				Resources/images,
			);
			path = "bili Extension";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C3FF09862E141EED003EBFE3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09A22E141EEE003EBFE3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09AC2E141EEE003EBFE3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09B82E141EEE003EBFE3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C3FF09802E141EED003EBFE3 = {
			isa = PBXGroup;
			children = (
				C3FF098B2E141EED003EBFE3 /* bili */,
				C3FF09A82E141EEE003EBFE3 /* biliTests */,
				C3FF09B22E141EEE003EBFE3 /* biliUITests */,
				C3FF09BF2E141EEE003EBFE3 /* bili Extension */,
				C3FF098A2E141EED003EBFE3 /* Products */,
			);
			sourceTree = "<group>";
		};
		C3FF098A2E141EED003EBFE3 /* Products */ = {
			isa = PBXGroup;
			children = (
				C3FF09892E141EED003EBFE3 /* bili.app */,
				C3FF09A52E141EEE003EBFE3 /* biliTests.xctest */,
				C3FF09AF2E141EEE003EBFE3 /* biliUITests.xctest */,
				C3FF09BB2E141EEE003EBFE3 /* bili Extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C3FF09882E141EED003EBFE3 /* bili */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C3FF09DC2E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "bili" */;
			buildPhases = (
				C3FF09852E141EED003EBFE3 /* Sources */,
				C3FF09862E141EED003EBFE3 /* Frameworks */,
				C3FF09872E141EED003EBFE3 /* Resources */,
				C3FF09DB2E141EEE003EBFE3 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				C3FF09BE2E141EEE003EBFE3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C3FF098B2E141EED003EBFE3 /* bili */,
			);
			name = bili;
			packageProductDependencies = (
			);
			productName = bili;
			productReference = C3FF09892E141EED003EBFE3 /* bili.app */;
			productType = "com.apple.product-type.application";
		};
		C3FF09A42E141EEE003EBFE3 /* biliTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C3FF09DF2E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "biliTests" */;
			buildPhases = (
				C3FF09A12E141EEE003EBFE3 /* Sources */,
				C3FF09A22E141EEE003EBFE3 /* Frameworks */,
				C3FF09A32E141EEE003EBFE3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C3FF09A72E141EEE003EBFE3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C3FF09A82E141EEE003EBFE3 /* biliTests */,
			);
			name = biliTests;
			packageProductDependencies = (
			);
			productName = biliTests;
			productReference = C3FF09A52E141EEE003EBFE3 /* biliTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		C3FF09AE2E141EEE003EBFE3 /* biliUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C3FF09E22E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "biliUITests" */;
			buildPhases = (
				C3FF09AB2E141EEE003EBFE3 /* Sources */,
				C3FF09AC2E141EEE003EBFE3 /* Frameworks */,
				C3FF09AD2E141EEE003EBFE3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C3FF09B12E141EEE003EBFE3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C3FF09B22E141EEE003EBFE3 /* biliUITests */,
			);
			name = biliUITests;
			packageProductDependencies = (
			);
			productName = biliUITests;
			productReference = C3FF09AF2E141EEE003EBFE3 /* biliUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		C3FF09BA2E141EEE003EBFE3 /* bili Extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C3FF09D62E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "bili Extension" */;
			buildPhases = (
				C3FF09B72E141EEE003EBFE3 /* Sources */,
				C3FF09B82E141EEE003EBFE3 /* Frameworks */,
				C3FF09B92E141EEE003EBFE3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C3FF09BF2E141EEE003EBFE3 /* bili Extension */,
			);
			name = "bili Extension";
			packageProductDependencies = (
			);
			productName = "bili Extension";
			productReference = C3FF09BB2E141EEE003EBFE3 /* bili Extension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C3FF09812E141EED003EBFE3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					C3FF09882E141EED003EBFE3 = {
						CreatedOnToolsVersion = 16.4;
					};
					C3FF09A42E141EEE003EBFE3 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = C3FF09882E141EED003EBFE3;
					};
					C3FF09AE2E141EEE003EBFE3 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = C3FF09882E141EED003EBFE3;
					};
					C3FF09BA2E141EEE003EBFE3 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = C3FF09842E141EED003EBFE3 /* Build configuration list for PBXProject "bili" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C3FF09802E141EED003EBFE3;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = C3FF098A2E141EED003EBFE3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C3FF09882E141EED003EBFE3 /* bili */,
				C3FF09A42E141EEE003EBFE3 /* biliTests */,
				C3FF09AE2E141EEE003EBFE3 /* biliUITests */,
				C3FF09BA2E141EEE003EBFE3 /* bili Extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C3FF09872E141EED003EBFE3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09A32E141EEE003EBFE3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09AD2E141EEE003EBFE3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09B92E141EEE003EBFE3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C3FF09852E141EED003EBFE3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09A12E141EEE003EBFE3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09AB2E141EEE003EBFE3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C3FF09B72E141EEE003EBFE3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C3FF09A72E141EEE003EBFE3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C3FF09882E141EED003EBFE3 /* bili */;
			targetProxy = C3FF09A62E141EEE003EBFE3 /* PBXContainerItemProxy */;
		};
		C3FF09B12E141EEE003EBFE3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C3FF09882E141EED003EBFE3 /* bili */;
			targetProxy = C3FF09B02E141EEE003EBFE3 /* PBXContainerItemProxy */;
		};
		C3FF09BE2E141EEE003EBFE3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C3FF09BA2E141EEE003EBFE3 /* bili Extension */;
			targetProxy = C3FF09BD2E141EEE003EBFE3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C3FF09D72E141EEE003EBFE3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "bili Extension/bili_Extension.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "bili Extension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "bili Extension";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					SafariServices,
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.bili.Extension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		C3FF09D82E141EEE003EBFE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "bili Extension/bili_Extension.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "bili Extension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "bili Extension";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					SafariServices,
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.bili.Extension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		C3FF09D92E141EEE003EBFE3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C3FF09DA2E141EEE003EBFE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		C3FF09DD2E141EEE003EBFE3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = bili/bili.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = bili;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					SafariServices,
					"-framework",
					WebKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.bili;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		C3FF09DE2E141EEE003EBFE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = bili/bili.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = bili;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-framework",
					SafariServices,
					"-framework",
					WebKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.bili;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		C3FF09E02E141EEE003EBFE3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.biliTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bili.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/bili";
			};
			name = Debug;
		};
		C3FF09E12E141EEE003EBFE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.biliTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bili.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/bili";
			};
			name = Release;
		};
		C3FF09E32E141EEE003EBFE3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.biliUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = bili;
			};
			name = Debug;
		};
		C3FF09E42E141EEE003EBFE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3QC477X64J;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.suay.biliUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = bili;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C3FF09842E141EED003EBFE3 /* Build configuration list for PBXProject "bili" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3FF09D92E141EEE003EBFE3 /* Debug */,
				C3FF09DA2E141EEE003EBFE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C3FF09D62E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "bili Extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3FF09D72E141EEE003EBFE3 /* Debug */,
				C3FF09D82E141EEE003EBFE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C3FF09DC2E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "bili" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3FF09DD2E141EEE003EBFE3 /* Debug */,
				C3FF09DE2E141EEE003EBFE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C3FF09DF2E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "biliTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3FF09E02E141EEE003EBFE3 /* Debug */,
				C3FF09E12E141EEE003EBFE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C3FF09E22E141EEE003EBFE3 /* Build configuration list for PBXNativeTarget "biliUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C3FF09E32E141EEE003EBFE3 /* Debug */,
				C3FF09E42E141EEE003EBFE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C3FF09812E141EED003EBFE3 /* Project object */;
}
