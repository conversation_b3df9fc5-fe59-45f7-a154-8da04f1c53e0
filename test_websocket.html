<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
</head>
<body>
    <h1>WebSocket 连接测试</h1>
    <div id="status">正在连接...</div>
    <div id="messages" style="height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; margin: 10px 0;"></div>
    <button onclick="testConnection()">测试连接</button>
    <button onclick="sendTestMessage()">发送测试消息</button>
    <button onclick="sendPlaybackRate()">发送倍速指令</button>
    <button onclick="sendSkipIntro()">发送跳过片头指令</button>
    <button onclick="clearMessages()">清空消息</button>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function log(message) {
            console.log(message);
            messagesDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function connectWebSocket() {
            try {
                log('尝试连接到 ws://localhost:8001/scp');
                ws = new WebSocket('ws://localhost:8001/scp');

                ws.onopen = function(event) {
                    log('WebSocket 连接已建立');
                    statusDiv.textContent = '已连接';
                    statusDiv.style.color = 'green';
                };

                ws.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.action) {
                            log('解析消息: action=' + data.action);
                        }
                    } catch (e) {
                        log('消息解析失败: ' + e);
                    }
                };

                ws.onerror = function(error) {
                    log('WebSocket 错误: ' + error);
                    statusDiv.textContent = '连接错误';
                    statusDiv.style.color = 'red';
                };

                ws.onclose = function(event) {
                    log('连接已关闭 - 代码: ' + event.code + ', 原因: ' + event.reason);
                    statusDiv.textContent = '连接已关闭';
                    statusDiv.style.color = 'orange';

                    // 自动重连
                    setTimeout(() => {
                        log('尝试自动重连...');
                        connectWebSocket();
                    }, 3000);
                };

            } catch (error) {
                log('创建WebSocket失败: ' + error);
                statusDiv.textContent = '创建失败';
                statusDiv.style.color = 'red';
            }
        }

        function testConnection() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('连接状态: 已连接');
            } else {
                log('连接状态: ' + (ws ? ws.readyState : '未初始化'));
            }
        }

        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMsg = JSON.stringify({action: 'test', message: 'Hello from browser'});
                ws.send(testMsg);
                log('发送测试消息: ' + testMsg);
            } else {
                log('无法发送消息 - WebSocket未连接');
            }
        }

        function sendPlaybackRate() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const msg = JSON.stringify({action: 'setPlaybackRate', rate: 1.5});
                ws.send(msg);
                log('发送倍速指令: ' + msg);
            } else {
                log('无法发送消息 - WebSocket未连接');
            }
        }

        function sendSkipIntro() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const msg = JSON.stringify({action: 'skipIntro'});
                ws.send(msg);
                log('发送跳过片头指令: ' + msg);
            } else {
                log('无法发送消息 - WebSocket未连接');
            }
        }

        function clearMessages() {
            messagesDiv.innerHTML = '';
        }

        // 页面加载时自动连接
        connectWebSocket();
    </script>
</body>
</html>
