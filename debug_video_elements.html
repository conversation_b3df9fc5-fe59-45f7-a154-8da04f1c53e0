<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bilibili视频元素调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .video-info { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; background: #007bff; color: white; }
        .success { background: #28a745; }
        .warning { background: #ffc107; color: black; }
        .error { background: #dc3545; }
        #log { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Bilibili视频元素调试工具</h1>
    
    <div class="section">
        <h3>1. 视频元素检测</h3>
        <button onclick="detectVideoElements()">检测所有视频元素</button>
        <button onclick="testPlaybackRateChange()">测试倍速修改</button>
        <button onclick="testDirectVideoControl()">直接控制测试</button>
        <div id="video-elements"></div>
    </div>
    
    <div class="section">
        <h3>2. Bilibili特定检测</h3>
        <button onclick="detectBilibiliPlayer()">检测Bilibili播放器</button>
        <button onclick="detectPlayerContainers()">检测播放器容器</button>
        <button onclick="waitForVideoLoad()">等待视频加载</button>
    </div>
    
    <div class="section">
        <h3>3. 实时监控</h3>
        <button onclick="startVideoMonitoring()">开始监控</button>
        <button onclick="stopVideoMonitoring()">停止监控</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        const logDiv = document.getElementById('log');
        const videoElementsDiv = document.getElementById('video-elements');
        let monitoringInterval = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            const color = type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : type === 'success' ? '#28a745' : '#007bff';
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${color};">${message}</span>`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function detectVideoElements() {
            log('=== 开始检测视频元素 ===');
            videoElementsDiv.innerHTML = '';
            
            const videos = document.querySelectorAll('video');
            log(`找到 ${videos.length} 个video元素`);
            
            if (videos.length === 0) {
                videoElementsDiv.innerHTML = '<div class="video-info error">未找到任何video元素</div>';
                return;
            }
            
            videos.forEach((video, index) => {
                const info = {
                    index: index + 1,
                    src: video.src || video.currentSrc || '无',
                    width: video.offsetWidth,
                    height: video.offsetHeight,
                    visible: video.offsetWidth > 0 && video.offsetHeight > 0,
                    currentTime: video.currentTime.toFixed(2),
                    duration: video.duration ? video.duration.toFixed(2) : '未知',
                    playbackRate: video.playbackRate,
                    paused: video.paused,
                    muted: video.muted,
                    volume: video.volume.toFixed(2),
                    readyState: video.readyState,
                    networkState: video.networkState,
                    className: video.className || '无',
                    id: video.id || '无',
                    parentElement: video.parentElement ? video.parentElement.tagName : '无'
                };
                
                const infoDiv = document.createElement('div');
                infoDiv.className = 'video-info';
                infoDiv.innerHTML = `
                    <strong>Video ${info.index}:</strong><br>
                    - src: ${info.src}<br>
                    - 尺寸: ${info.width}x${info.height} (可见: ${info.visible})<br>
                    - 时间: ${info.currentTime}s / ${info.duration}s<br>
                    - 倍速: ${info.playbackRate}x<br>
                    - 状态: ${info.paused ? '暂停' : '播放'}<br>
                    - 音量: ${info.volume} (静音: ${info.muted})<br>
                    - readyState: ${info.readyState}<br>
                    - className: ${info.className}<br>
                    - id: ${info.id}<br>
                    - 父元素: ${info.parentElement}<br>
                    <button onclick="testVideoControl(${index})" style="margin-top: 5px; padding: 3px 8px; font-size: 11px;">测试控制</button>
                `;
                videoElementsDiv.appendChild(infoDiv);
                
                log(`Video ${info.index}: ${info.width}x${info.height}, 倍速=${info.playbackRate}, 状态=${info.paused ? '暂停' : '播放'}`);
            });
        }
        
        function testVideoControl(videoIndex) {
            const videos = document.querySelectorAll('video');
            if (videoIndex >= videos.length) {
                log(`Video ${videoIndex + 1} 不存在`, 'error');
                return;
            }
            
            const video = videos[videoIndex];
            log(`测试控制 Video ${videoIndex + 1}...`);
            
            try {
                const oldRate = video.playbackRate;
                const newRate = 1.5;
                
                log(`设置倍速: ${oldRate} -> ${newRate}`);
                video.playbackRate = newRate;
                
                setTimeout(() => {
                    const actualRate = video.playbackRate;
                    if (Math.abs(actualRate - newRate) < 0.01) {
                        log(`✓ 倍速设置成功: ${actualRate}`, 'success');
                    } else {
                        log(`✗ 倍速设置失败: 期望${newRate}, 实际${actualRate}`, 'error');
                    }
                }, 100);
                
            } catch (error) {
                log(`✗ 控制失败: ${error}`, 'error');
            }
        }
        
        function testPlaybackRateChange() {
            log('=== 测试倍速修改 ===');
            const videos = document.querySelectorAll('video');
            
            if (videos.length === 0) {
                log('未找到视频元素', 'error');
                return;
            }
            
            const testRates = [0.5, 1.0, 1.25, 1.5, 2.0];
            let currentTestIndex = 0;
            
            function testNextRate() {
                if (currentTestIndex >= testRates.length) {
                    log('所有倍速测试完成', 'success');
                    return;
                }
                
                const rate = testRates[currentTestIndex];
                log(`测试倍速: ${rate}x`);
                
                videos.forEach((video, index) => {
                    try {
                        const oldRate = video.playbackRate;
                        video.playbackRate = rate;
                        
                        setTimeout(() => {
                            const actualRate = video.playbackRate;
                            log(`Video ${index + 1}: ${oldRate} -> ${actualRate} (目标: ${rate})`);
                        }, 50);
                        
                    } catch (error) {
                        log(`Video ${index + 1} 设置失败: ${error}`, 'error');
                    }
                });
                
                currentTestIndex++;
                setTimeout(testNextRate, 1000);
            }
            
            testNextRate();
        }
        
        function testDirectVideoControl() {
            log('=== 直接视频控制测试 ===');
            
            // 尝试多种选择器
            const selectors = [
                'video',
                '.bpx-player-video-wrap video',
                '.bilibili-player-video video',
                '[data-video] video',
                '.player-wrap video',
                '#bilibili-player video',
                '.bpx-player-container video'
            ];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                log(`选择器 "${selector}": 找到 ${elements.length} 个元素`);
                
                elements.forEach((element, index) => {
                    if (element.tagName === 'VIDEO') {
                        log(`  - Video ${index + 1}: ${element.offsetWidth}x${element.offsetHeight}, 倍速=${element.playbackRate}`);
                        
                        // 测试设置倍速
                        try {
                            const oldRate = element.playbackRate;
                            element.playbackRate = 1.75;
                            setTimeout(() => {
                                log(`    倍速测试: ${oldRate} -> ${element.playbackRate}`, 
                                    element.playbackRate === 1.75 ? 'success' : 'warning');
                            }, 50);
                        } catch (error) {
                            log(`    倍速设置失败: ${error}`, 'error');
                        }
                    }
                });
            });
        }
        
        function detectBilibiliPlayer() {
            log('=== 检测Bilibili播放器结构 ===');
            
            const playerSelectors = [
                '.bpx-player',
                '.bpx-player-container',
                '.bpx-player-video-wrap',
                '.bilibili-player',
                '.bilibili-player-video',
                '#bilibili-player',
                '.player-wrap',
                '[data-video]'
            ];
            
            playerSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    log(`✓ 找到播放器容器: ${selector} (${elements.length}个)`, 'success');
                    
                    elements.forEach((element, index) => {
                        const videos = element.querySelectorAll('video');
                        log(`  容器 ${index + 1}: 包含 ${videos.length} 个video元素`);
                        
                        videos.forEach((video, videoIndex) => {
                            log(`    Video ${videoIndex + 1}: ${video.offsetWidth}x${video.offsetHeight}, 倍速=${video.playbackRate}`);
                        });
                    });
                } else {
                    log(`✗ 未找到: ${selector}`, 'warning');
                }
            });
        }
        
        function detectPlayerContainers() {
            log('=== 检测所有可能的播放器容器 ===');
            
            // 检查所有包含video的元素
            const allElements = document.querySelectorAll('*');
            const containersWithVideo = [];
            
            allElements.forEach(element => {
                const videos = element.querySelectorAll('video');
                if (videos.length > 0 && element !== document.body && element !== document.documentElement) {
                    containersWithVideo.push({
                        element: element,
                        tagName: element.tagName,
                        className: element.className || '无',
                        id: element.id || '无',
                        videoCount: videos.length
                    });
                }
            });
            
            log(`找到 ${containersWithVideo.length} 个包含video的容器`);
            
            containersWithVideo.forEach((container, index) => {
                log(`容器 ${index + 1}: <${container.tagName}> class="${container.className}" id="${container.id}" (${container.videoCount}个video)`);
            });
        }
        
        function waitForVideoLoad() {
            log('=== 等待视频加载 ===');
            
            let attempts = 0;
            const maxAttempts = 20;
            
            const checkInterval = setInterval(() => {
                attempts++;
                const videos = document.querySelectorAll('video');
                const readyVideos = Array.from(videos).filter(v => v.readyState >= 2);
                
                log(`尝试 ${attempts}/${maxAttempts}: 找到 ${videos.length} 个video, ${readyVideos.length} 个已就绪`);
                
                if (readyVideos.length > 0 || attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    
                    if (readyVideos.length > 0) {
                        log(`✓ 视频加载完成, ${readyVideos.length} 个视频已就绪`, 'success');
                        detectVideoElements();
                    } else {
                        log('✗ 等待超时，视频未加载完成', 'error');
                    }
                }
            }, 500);
        }
        
        function startVideoMonitoring() {
            if (monitoringInterval) {
                log('监控已在运行中', 'warning');
                return;
            }
            
            log('开始视频监控...', 'success');
            
            monitoringInterval = setInterval(() => {
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    videos.forEach((video, index) => {
                        if (video.offsetWidth > 0 && video.offsetHeight > 0) {
                            log(`Video ${index + 1}: 时间=${video.currentTime.toFixed(1)}s, 倍速=${video.playbackRate}x, 状态=${video.paused ? '暂停' : '播放'}`);
                        }
                    });
                } else {
                    log('未检测到视频元素', 'warning');
                }
            }, 2000);
        }
        
        function stopVideoMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('监控已停止', 'success');
            } else {
                log('监控未运行', 'warning');
            }
        }
        
        function clearLog() {
            logDiv.innerHTML = '';
        }
        
        // 页面加载时自动检测
        window.onload = function() {
            log('页面加载完成，开始自动检测...');
            setTimeout(() => {
                detectVideoElements();
                detectBilibiliPlayer();
            }, 1000);
        };
    </script>
</body>
</html>
