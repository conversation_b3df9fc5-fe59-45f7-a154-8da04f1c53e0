#!/usr/bin/env python3
"""
简单的WebSocket测试服务器
用于测试Safari插件连接
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleWebSocketServer:
    def __init__(self, host='localhost', port=8003):
        self.host = host
        self.port = port
        self.clients = set()
        
    async def register_client(self, websocket, path):
        """注册新客户端"""
        self.clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        
        logger.info(f"✓ 客户端已连接: {client_info} 路径: {path} (总计: {len(self.clients)})")
        
        # 发送欢迎消息
        welcome_msg = {
            "type": "welcome",
            "message": "WebSocket连接成功",
            "server": f"ws://{self.host}:{self.port}{path}",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "client_count": len(self.clients)
        }
        
        try:
            await websocket.send(json.dumps(welcome_msg))
            logger.info(f"发送欢迎消息: {welcome_msg}")
        except Exception as e:
            logger.error(f"发送欢迎消息失败: {e}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        logger.info(f"客户端已断开 (剩余: {len(self.clients)})")
    
    async def handle_message(self, websocket, message, path):
        """处理收到的消息"""
        try:
            data = json.loads(message)
            logger.info(f"收到消息: {data}")
            
            # 回复确认消息
            response = {
                "status": "received",
                "original": data,
                "timestamp": int(datetime.now().timestamp() * 1000),
                "server_time": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(response))
            logger.info(f"已回复: {response}")
            
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
            error_response = {
                "error": "Invalid JSON format",
                "received": message,
                "timestamp": int(datetime.now().timestamp() * 1000)
            }
            await websocket.send(json.dumps(error_response))
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    async def client_handler(self, websocket, path):
        """处理客户端连接"""
        logger.info(f"新连接请求: {path} from {websocket.remote_address}")
        
        await self.register_client(websocket, path)
        
        try:
            async for message in websocket:
                await self.handle_message(websocket, message, path)
        except websockets.exceptions.ConnectionClosed:
            logger.info("客户端连接正常关闭")
        except Exception as e:
            logger.error(f"客户端处理错误: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def send_periodic_messages(self):
        """定期发送测试消息"""
        await asyncio.sleep(10)  # 等待客户端连接
        
        test_commands = [
            {"command": "setPlaybackRate", "value": 1.5},
            {"command": "setPlaybackRate", "value": 2.0},
            {"command": "setPlaybackRate", "value": 1.0},
            {"command": "test", "message": "Hello from server"}
        ]
        
        command_index = 0
        
        while True:
            if self.clients:
                cmd = test_commands[command_index % len(test_commands)]
                cmd["timestamp"] = int(datetime.now().timestamp() * 1000)
                cmd["sequence"] = command_index + 1
                
                logger.info(f"发送定期消息 #{command_index + 1}: {cmd}")
                
                # 发送给所有客户端
                for client in self.clients.copy():
                    try:
                        await client.send(json.dumps(cmd))
                    except:
                        self.clients.discard(client)
                
                command_index += 1
                await asyncio.sleep(15)  # 每15秒发送一个消息
            else:
                await asyncio.sleep(5)
    
    async def start_server(self):
        """启动WebSocket服务器"""
        logger.info(f"启动简单WebSocket测试服务器...")
        logger.info(f"地址: ws://{self.host}:{self.port}/scp")
        
        try:
            server = await websockets.serve(
                self.client_handler,
                self.host,
                self.port
            )
            
            logger.info("✓ WebSocket服务器已启动，等待客户端连接...")
            
            # 同时启动定期消息发送
            await asyncio.gather(
                server.wait_closed(),
                self.send_periodic_messages()
            )
            
        except Exception as e:
            logger.error(f"WebSocket服务器启动失败: {e}")

async def main():
    """主函数"""
    server = SimpleWebSocketServer()
    await server.start_server()

if __name__ == "__main__":
    try:
        print("🚀 启动简单WebSocket测试服务器...")
        print("📱 Safari插件连接地址: ws://localhost:8003/scp")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 WebSocket服务器已停止")
    except Exception as e:
        print(f"❌ 服务器错误: {e}")
