<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari扩展检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #messages { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Safari扩展和WebSocket连接检查</h1>
    
    <div id="extension-status" class="status info">检查扩展状态中...</div>
    <div id="websocket-status" class="status info">检查WebSocket连接中...</div>
    
    <div>
        <button onclick="checkExtension()">检查扩展</button>
        <button onclick="testWebSocket()">测试WebSocket</button>
        <button onclick="sendTestCommand()">发送测试命令</button>
        <button onclick="clearMessages()">清空日志</button>
    </div>
    
    <div id="messages"></div>

    <script>
        const messagesDiv = document.getElementById('messages');
        const extensionStatusDiv = document.getElementById('extension-status');
        const websocketStatusDiv = document.getElementById('websocket-status');
        let ws = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
            messagesDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function checkExtension() {
            log('开始检查Safari扩展...');
            
            // 检查是否在Safari中
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            log(`浏览器检测: ${navigator.userAgent}`);
            log(`是否为Safari: ${isSafari}`);
            
            // 检查扩展API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('✓ Chrome扩展API可用');
                extensionStatusDiv.textContent = '扩展API可用';
                extensionStatusDiv.className = 'status success';
                
                // 尝试发送消息到扩展
                if (chrome.runtime.sendMessage) {
                    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                        if (chrome.runtime.lastError) {
                            log('✗ 扩展通信失败: ' + chrome.runtime.lastError.message);
                        } else {
                            log('✓ 扩展通信成功: ' + JSON.stringify(response));
                        }
                    });
                }
            } else {
                log('✗ Chrome扩展API不可用');
                extensionStatusDiv.textContent = '扩展API不可用';
                extensionStatusDiv.className = 'status error';
            }
        }

        function testWebSocket() {
            log('开始测试WebSocket连接...');
            
            try {
                ws = new WebSocket('ws://localhost:8001/scp');
                
                ws.onopen = function(event) {
                    log('✓ WebSocket连接成功建立');
                    websocketStatusDiv.textContent = 'WebSocket已连接';
                    websocketStatusDiv.className = 'status success';
                };
                
                ws.onmessage = function(event) {
                    log('收到WebSocket消息: ' + event.data);
                };
                
                ws.onerror = function(error) {
                    log('✗ WebSocket连接错误: ' + error);
                    websocketStatusDiv.textContent = 'WebSocket连接错误';
                    websocketStatusDiv.className = 'status error';
                };
                
                ws.onclose = function(event) {
                    log(`WebSocket连接关闭 - 代码: ${event.code}, 原因: ${event.reason}`);
                    websocketStatusDiv.textContent = 'WebSocket已关闭';
                    websocketStatusDiv.className = 'status error';
                };
                
            } catch (error) {
                log('✗ 创建WebSocket失败: ' + error);
                websocketStatusDiv.textContent = 'WebSocket创建失败';
                websocketStatusDiv.className = 'status error';
            }
        }

        function sendTestCommand() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testCmd = {
                    action: 'setPlaybackRate',
                    rate: 1.5,
                    timestamp: Date.now()
                };
                ws.send(JSON.stringify(testCmd));
                log('发送测试命令: ' + JSON.stringify(testCmd));
            } else {
                log('✗ WebSocket未连接，无法发送命令');
            }
        }

        function clearMessages() {
            messagesDiv.innerHTML = '';
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('页面加载完成，开始自动检查...');
            checkExtension();
            setTimeout(testWebSocket, 1000);
        };
    </script>
</body>
</html>
