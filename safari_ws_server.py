#!/usr/bin/env python3
"""
Safari插件专用WebSocket服务器
监听8002端口，处理Safari插件的连接和命令
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 存储连接的客户端
connected_clients = set()

async def handle_client(websocket, path):
    """处理客户端连接"""
    client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    logger.info(f"🔗 新客户端连接: {client_addr} (路径: {path})")
    
    # 添加到连接列表
    connected_clients.add(websocket)
    
    try:
        # 发送欢迎消息
        welcome_msg = {
            "type": "welcome",
            "message": "Safari插件WebSocket服务器连接成功",
            "timestamp": datetime.now().isoformat(),
            "server": "safari_ws_server"
        }
        await websocket.send(json.dumps(welcome_msg))
        logger.info(f"✅ 发送欢迎消息给 {client_addr}")
        
        # 监听消息
        async for message in websocket:
            logger.info(f"📨 收到来自 {client_addr} 的消息: {message}")
            
            try:
                # 尝试解析JSON消息
                data = json.loads(message)
                await handle_message(websocket, data, client_addr)
            except json.JSONDecodeError:
                # 处理非JSON消息
                logger.info(f"📝 收到文本消息: {message}")
                response = {
                    "type": "echo",
                    "original_message": message,
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(response))
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"🔌 客户端 {client_addr} 断开连接")
    except Exception as e:
        logger.error(f"❌ 处理客户端 {client_addr} 时出错: {e}")
    finally:
        # 从连接列表中移除
        connected_clients.discard(websocket)
        logger.info(f"🗑️ 移除客户端 {client_addr}，当前连接数: {len(connected_clients)}")

async def handle_message(websocket, data, client_addr):
    """处理收到的JSON消息"""
    message_type = data.get('type', 'unknown')
    logger.info(f"🎯 处理消息类型: {message_type}")
    
    if message_type == 'safari_plugin_connected':
        # Safari插件连接确认
        response = {
            "type": "connection_confirmed",
            "message": "Safari插件连接已确认",
            "client_info": data,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))
        logger.info(f"✅ 确认Safari插件连接: {client_addr}")
        
    elif message_type == 'test':
        # 测试消息
        response = {
            "type": "test_response",
            "message": "测试消息收到",
            "original": data,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))
        logger.info(f"🧪 回复测试消息给 {client_addr}")
        
    elif data.get('command') == 'setPlaybackRate':
        # 倍速控制命令
        rate = data.get('value', 1.0)
        logger.info(f"🎵 收到倍速命令: {rate}x")
        
        # 广播给所有连接的客户端
        broadcast_msg = {
            "command": "setPlaybackRate",
            "value": rate,
            "timestamp": datetime.now().isoformat(),
            "from_server": True
        }
        
        await broadcast_message(broadcast_msg)
        
        # 回复确认
        response = {
            "type": "command_executed",
            "command": "setPlaybackRate",
            "value": rate,
            "message": f"倍速已设置为 {rate}x",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))
        
    else:
        # 未知消息类型
        response = {
            "type": "unknown_message",
            "message": "未知的消息类型",
            "original": data,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))
        logger.warning(f"❓ 未知消息类型: {message_type}")

async def broadcast_message(message):
    """向所有连接的客户端广播消息"""
    if not connected_clients:
        logger.info("📢 没有连接的客户端，跳过广播")
        return
    
    message_str = json.dumps(message)
    logger.info(f"📢 广播消息给 {len(connected_clients)} 个客户端: {message_str}")
    
    # 创建发送任务
    tasks = []
    for client in connected_clients.copy():  # 使用副本避免并发修改
        tasks.append(send_safe(client, message_str))
    
    # 并发发送
    if tasks:
        await asyncio.gather(*tasks, return_exceptions=True)

async def send_safe(websocket, message):
    """安全发送消息，处理连接错误"""
    try:
        await websocket.send(message)
    except websockets.exceptions.ConnectionClosed:
        logger.info("🔌 客户端连接已关闭，移除")
        connected_clients.discard(websocket)
    except Exception as e:
        logger.error(f"❌ 发送消息失败: {e}")
        connected_clients.discard(websocket)

async def send_test_command():
    """定期发送测试命令（用于测试）"""
    while True:
        await asyncio.sleep(30)  # 每30秒发送一次
        if connected_clients:
            test_msg = {
                "command": "setPlaybackRate",
                "value": 1.25,
                "timestamp": datetime.now().isoformat(),
                "from_server": True,
                "note": "服务器测试命令"
            }
            await broadcast_message(test_msg)

async def main():
    """启动WebSocket服务器"""
    host = "localhost"
    port = 8002
    
    print("🚀 启动Safari插件WebSocket服务器...")
    print(f"📱 Safari插件连接地址: ws://{host}:{port}/scp")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    logger.info("启动Safari插件WebSocket服务器...")
    logger.info(f"地址: ws://{host}:{port}/scp")
    
    try:
        # 启动WebSocket服务器
        server = await websockets.serve(
            handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10
        )
        
        logger.info(f"✓ WebSocket服务器已启动，等待Safari插件连接...")
        
        # 启动测试命令任务（可选）
        # asyncio.create_task(send_test_command())
        
        # 保持服务器运行
        await server.wait_closed()
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            logger.error(f"❌ 端口 {port} 已被占用")
            print(f"\n❌ 错误: 端口 {port} 已被占用")
            print("请先停止占用该端口的服务，或使用其他端口")
        else:
            logger.error(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        logger.info("👋 收到停止信号，关闭服务器...")
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    asyncio.run(main())
